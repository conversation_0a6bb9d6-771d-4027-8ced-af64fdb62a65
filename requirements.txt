# E-Sign QA MCP Platform Dependencies

# Core Framework
fastapi>=0.100.0
uvicorn[standard]>=0.23.0
pydantic>=2.0.0
pydantic-settings>=2.0.0

# MCP Protocol Support
fastapi-mcp>=0.1.0

# HTTP Client
requests>=2.28.0
httpx>=0.24.0

# Configuration Management
pyyaml>=6.0
python-dotenv>=1.0.0

# Logging and Monitoring
structlog>=23.0.0
psutil>=5.9.0

# Template and Code Generation
jinja2>=3.1.0

# Date and Time
python-dateutil>=2.8.0

# JSON Processing
orjson>=3.8.0

# Async Support
asyncio-mqtt>=0.13.0
aiofiles>=23.0.0

# Development Dependencies (optional)
# Uncomment for development environment

# Testing
# pytest>=7.0.0
# pytest-asyncio>=0.21.0
# pytest-cov>=4.0.0
# httpx>=0.24.0  # for TestClient

# Code Quality
# black>=23.0.0
# isort>=5.12.0
# flake8>=6.0.0
# mypy>=1.0.0

# Documentation
# mkdocs>=1.4.0
# mkdocs-material>=9.0.0

# Performance Monitoring
# prometheus-client>=0.16.0

# Security
# cryptography>=40.0.0

# Database (if needed)
# sqlalchemy>=2.0.0
# alembic>=1.10.0

# Caching (if needed)
# redis>=4.5.0

# Message Queue (if needed)
# celery>=5.2.0

# Production Server
gunicorn>=20.1.0

# Environment-specific dependencies
# Add these based on your deployment environment