#!/bin/bash

# 测试通过文件创建签署流程的curl命令

echo "正在测试create_flow_by_file API..."

curl -X POST "http://localhost:8000/signing/create_flow_by_file" \
  -H "Content-Type: application/json" \
  -d '{
    "file_id": "63573ed91cee4166b532c87a6e83a7c1",
    "file_name": "测试合同.pdf",
    "signer_account_id": "6300761d28b6447b98eea4589db35a18",
    "signer_name": "测试用户",
    "signer_mobile": "***********",
    "signer_idcard": "340822199712270158",
    "flow_title": "MCP工具测试签署流程",
    "pos_x": 200,
    "pos_y": 600,
    "pos_page": "1",
    "environment": "测试环境"
  }' \
  -v

echo -e "\n\n测试完成!"
