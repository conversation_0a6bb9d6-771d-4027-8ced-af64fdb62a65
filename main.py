#!/usr/bin/env python3
"""
主启动文件 - 使用 fastapi_mcp 自动暴露 MCP 工具
基于官方FastMCP框架，不做魔改，只做连接保持优化
"""
import os
import time
import uvicorn
import logging
import asyncio
import json
from fastapi import FastAPI, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import StreamingResponse
from fastapi_mcp import FastApiMCP

from app.core.config import *
from app.core.connection_keeper import get_connection_keeper

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# 服务配置
SERVER_HOST = os.getenv("HOST", "0.0.0.0")
SERVER_PORT = int(os.getenv("PORT", "8000"))
LOG_LEVEL = os.getenv("LOG_LEVEL", "INFO")

# 连接保持配置 - 针对SSE长连接优化
KEEP_ALIVE_TIMEOUT = int(os.getenv("KEEP_ALIVE_TIMEOUT", "604800"))  # 7天
GRACEFUL_SHUTDOWN_TIMEOUT = int(os.getenv("GRACEFUL_SHUTDOWN_TIMEOUT", "300"))  # 5分钟
MAX_CONCURRENCY = int(os.getenv("MAX_CONCURRENCY", "1000"))
MAX_REQUESTS = int(os.getenv("MAX_REQUESTS", "10000"))

def create_sse_headers() -> dict:
    """创建SSE专用响应头 - 确保连接不断"""
    return {
        "Cache-Control": "no-cache, no-store, must-revalidate",
        "Pragma": "no-cache",
        "Expires": "0",
        "X-Accel-Buffering": "no",  # 关键：禁用Nginx缓冲
        "Connection": "keep-alive",
        "Content-Type": "text/event-stream",
        "Access-Control-Allow-Origin": "*",
        "Access-Control-Allow-Headers": "*",
        "Access-Control-Allow-Methods": "*"
    }

def create_app() -> FastAPI:
    """创建FastAPI应用"""
    app = FastAPI(
        title=PROJECT_NAME,
        description=MCP_DESCRIPTION,
        version=VERSION,
        docs_url="/docs",
        redoc_url="/redoc"
    )

    # 添加CORS中间件
    app.add_middleware(
        CORSMiddleware,
        allow_origins=["*"],
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
    )

    # 加载业务域路由
    domain_modules = [
        "app.mcpController.domains.certificate_controller",
        "app.mcpController.domains.signing_controller", 
        "app.mcpController.domains.saas_controller",
        "app.mcpController.domains.identity_controller",
        "app.mcpController.domains.intention_controller",
        "app.mcpController.domains.platform_controller"
    ]

    for module_path in domain_modules:
        try:
            module = __import__(module_path, fromlist=[''])
            if hasattr(module, 'router'):
                app.include_router(module.router)
                domain_name = module_path.split('.')[-1].replace('_controller', '')
                logger.info(f"{domain_name}路由加载成功")
        except Exception as e:
            logger.error(f"{module_path}路由加载失败: {str(e)}")

    # 启动事件 - 初始化连接保持器
    @app.on_event("startup")
    async def startup_event():
        """应用启动时初始化连接保持器"""
        connection_keeper = get_connection_keeper()
        await connection_keeper.start_background_tasks()
        logger.info("✅ 连接保持器已启动，支持连接不断和服务重启恢复")

    # 添加健康检查端点
    @app.get("/health_check")
    async def health_check():
        return {
            "code": 0,
            "message": "ok",
            "data": {
                "server": "esign-qa-mcp-platform",
                "fastmcp_enabled": True,
                "connection_keeper_enabled": True,
                "keep_alive_timeout": KEEP_ALIVE_TIMEOUT,
                "version": VERSION
            }
        }

    # 连接保持端点 - 基于FastMCP框架的连接不断功能
    @app.get("/mcp/connection-status")
    async def connection_status(request: Request):
        """获取连接状态 - 支持连接不断"""
        connection_keeper = get_connection_keeper()

        # 从请求头获取连接ID
        conn_id = request.headers.get("X-Connection-ID")

        # 如果有连接ID，更新活动时间
        if conn_id:
            await connection_keeper.update_connection_activity(conn_id)

        # 获取状态信息
        status = await connection_keeper.get_connection_status(conn_id)

        return {
            "success": True,
            "data": status,
            "message": "连接状态正常",
            "timestamp": int(time.time())
        }

    # 连接注册端点
    @app.post("/mcp/register-connection")
    async def register_connection(request: Request):
        """注册新连接 - 支持服务重启后恢复"""
        connection_keeper = get_connection_keeper()

        # 获取客户端信息
        client_info = {
            "user_agent": request.headers.get("user-agent", "unknown"),
            "client_ip": request.client.host if request.client else "unknown",
            "registered_at": time.time()
        }

        # 注册连接
        conn_id = await connection_keeper.register_connection(client_info=client_info)

        return {
            "success": True,
            "data": {
                "connection_id": conn_id,
                "status": "registered",
                "keep_alive_interval": 30,
                "auto_reconnect": True
            },
            "message": "连接注册成功"
        }

    # SSE心跳端点 - 确保MCP连接不断
    @app.get("/mcp/sse-heartbeat")
    async def sse_heartbeat(request: Request):
        """SSE心跳端点 - 基于FastMCP框架的连接保持"""
        connection_keeper = get_connection_keeper()

        # 获取或创建连接ID
        conn_id = request.headers.get("X-Connection-ID")
        if not conn_id:
            conn_id = await connection_keeper.register_connection()

        # 更新连接活动
        await connection_keeper.update_connection_activity(conn_id)

        # 获取连接状态
        status = await connection_keeper.get_connection_status(conn_id)

        # 添加SSE专用信息
        sse_data = {
            **status,
            "sse_enabled": True,
            "keep_alive": True,
            "auto_reconnect": True,
            "server": "esign-qa-mcp-platform",
            "mcp_compatible": True,
            "timestamp": time.time()
        }

        return Response(
            content=json.dumps(sse_data),
            media_type="application/json",
            headers=create_sse_headers()
        )

    # SSE流式心跳 - 持续保持MCP连接
    @app.get("/mcp/sse-stream")
    async def sse_stream(request: Request):
        """SSE流式心跳 - 确保MCP连接永不断开"""
        connection_keeper = get_connection_keeper()

        # 获取或创建连接ID
        conn_id = request.headers.get("X-Connection-ID")
        if not conn_id:
            conn_id = await connection_keeper.register_connection()

        async def heartbeat_generator():
            counter = 0
            try:
                while True:
                    counter += 1

                    # 更新连接活动
                    await connection_keeper.update_connection_activity(conn_id)

                    # 获取连接状态
                    status = await connection_keeper.get_connection_status(conn_id)

                    # 构建SSE数据
                    sse_data = {
                        **status,
                        "type": "heartbeat",
                        "counter": counter,
                        "sse_stream": True,
                        "mcp_ready": True,
                        "timestamp": time.time()
                    }

                    # SSE格式输出
                    yield f"data: {json.dumps(sse_data)}\n\n"

                    # 30秒心跳间隔
                    await asyncio.sleep(30)

            except asyncio.CancelledError:
                logger.info(f"SSE流式连接关闭: {conn_id}")
                await connection_keeper.remove_connection(conn_id)
            except Exception as e:
                logger.error(f"SSE流式连接错误: {str(e)}")
                await connection_keeper.remove_connection(conn_id)

        return StreamingResponse(
            heartbeat_generator(),
            media_type="text/event-stream",
            headers=create_sse_headers()
        )

    # MCP连接状态检查 - 兼容FastMCP框架
    @app.get("/mcp/status")
    async def mcp_status():
        """MCP服务状态检查 - 确保服务可用"""
        connection_keeper = get_connection_keeper()
        status = await connection_keeper.get_connection_status()

        return Response(
            content=json.dumps({
                "mcp_service": "running",
                "fastmcp_enabled": True,
                "sse_support": True,
                "connection_keeper": status,
                "endpoints": {
                    "mcp": "/mcp",
                    "sse_heartbeat": "/mcp/sse-heartbeat",
                    "sse_stream": "/mcp/sse-stream",
                    "connection_status": "/mcp/connection-status"
                }
            }),
            media_type="application/json",
            headers=create_sse_headers()
        )

    # 设置 MCP 服务 - 使用官方FastApiMCP框架
    setup_mcp_service(app)

    return app


def setup_mcp_service(app: FastAPI):
    """设置MCP服务 - 使用官方FastApiMCP框架 + SSE连接保持"""
    try:
        logger.info("🚀 设置MCP服务...")

        # 使用官方FastApiMCP框架，暴露所有功能域
        mcp = FastApiMCP(
            app,
            include_tags=["证书域", "签署域", "SaaS域", "实名域", "意愿域", "平台功能"]
        )

        # 挂载MCP服务
        mcp.mount()

        logger.info("✅ MCP服务设置完成 - 使用官方FastApiMCP框架")
        logger.info(f"🔌 MCP端点: http://localhost:{SERVER_PORT}/mcp")
        return mcp

    except Exception as e:
        logger.error(f"❌ MCP服务设置失败: {str(e)}")
        import traceback
        traceback.print_exc()
        raise


def main():
    """主函数"""
    try:
        app = create_app()
        
        logger.info("🚀 启动esign-qa-mcp-platform服务")
        logger.info(f"📚 API文档: http://localhost:{SERVER_PORT}/docs")
        logger.info(f"🔌 MCP端点: http://localhost:{SERVER_PORT}/mcp")
        
        # 连接保持优化启动配置
        logger.info(f"🔧 启动参数: host={SERVER_HOST}, port={SERVER_PORT}")
        logger.info(f"🔧 连接保持: keep_alive={KEEP_ALIVE_TIMEOUT}秒, graceful_shutdown={GRACEFUL_SHUTDOWN_TIMEOUT}秒")

        # 使用uvicorn启动，配置连接保持参数
        uvicorn.run(
            app,
            host=SERVER_HOST,
            port=SERVER_PORT,
            log_level=LOG_LEVEL.lower(),
            # 连接保持优化配置
            timeout_keep_alive=KEEP_ALIVE_TIMEOUT,
            timeout_graceful_shutdown=GRACEFUL_SHUTDOWN_TIMEOUT,
            access_log=True,
            # 启用WebSocket支持（FastMCP可能需要）
            ws_ping_interval=20,
            ws_ping_timeout=20
        )

    except KeyboardInterrupt:
        logger.info("👋 服务已停止")
    except Exception as e:
        logger.error(f"❌ 服务启动失败: {str(e)}")
        raise


if __name__ == "__main__":
    main()
