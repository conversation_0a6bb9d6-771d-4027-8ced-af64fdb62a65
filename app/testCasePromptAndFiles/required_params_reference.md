# 必填参数验证参考文档

## 测试用例示例

### 证书创建接口必填参数验证

#### TL-证书名称为空时的验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入空的证书名称
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示证书名称不能为空；

#### TL-手机号为空时的验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入空的手机号
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示手机号不能为空；

#### TL-身份证号为空时的验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入空的身份证号
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示身份证号不能为空；

## 常见必填参数类型

### 用户信息类
- 姓名 (name)
- 手机号 (phone/mobile)
- 身份证号 (idcard/idNo)
- 邮箱 (email)

### 业务数据类
- 订单号 (orderId)
- 流程ID (flowId)
- 证书ID (certId)
- 文档内容 (content)

### 配置参数类
- 应用ID (appId)
- 租户ID (tenantId)
- 环境类型 (environment)
- 接口版本 (version)

## 验证规则

### 参数存在性验证
- 参数是否传递
- 参数是否为null
- 参数是否为空字符串
- 参数是否为空白字符

### 参数格式验证
- 手机号格式
- 身份证号格式
- 邮箱格式
- 日期格式

### 参数长度验证
- 最小长度限制
- 最大长度限制
- 固定长度要求
- 字符数统计
