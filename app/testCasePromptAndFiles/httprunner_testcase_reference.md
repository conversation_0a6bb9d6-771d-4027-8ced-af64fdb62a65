# HttpRunner测试用例参考文档

## 完整测试用例示例

### 证书创建接口测试用例

```yaml
name: "证书创建接口测试"
config:
    name: "证书服务测试配置"
    base_url: "${ENV(base_url)}"
    variables:
        test_phone: "19848701010"
        test_idcard: "654322195105129970"
        test_name: "测试孔强庆"
        test_cert_name: "测试证书"
    verify: false

teststeps:
- name: "正常流程-创建个人证书"
  request:
    url: "/certificate/create"
    method: "POST"
    headers:
      Content-Type: "application/json"
      X-Tsign-Open-App-Id: "${ENV(app_id)}"
    json:
      cert_name: "${test_cert_name}"
      phone: "${test_phone}"
      idcard: "${test_idcard}"
      algorithm: "SM2"
      cert_time: "ONEYEAR"
  validate:
  - eq: ["status_code", 200]
  - eq: ["content.status", "success"]
  - contains: ["content.data.cert_id", ""]
  - eq: ["content.data.algorithm", "SM2"]
  extract:
  - cert_id: "content.data.cert_id"

- name: "异常场景-证书名称为空"
  request:
    url: "/certificate/create"
    method: "POST"
    headers:
      Content-Type: "application/json"
      X-Tsign-Open-App-Id: "${ENV(app_id)}"
    json:
      cert_name: ""
      phone: "${test_phone}"
      idcard: "${test_idcard}"
      algorithm: "SM2"
      cert_time: "ONEYEAR"
  validate:
  - eq: ["status_code", 400]
  - contains: ["content.message", "证书名称不能为空"]

- name: "边界测试-证书名称长度限制"
  request:
    url: "/certificate/create"
    method: "POST"
    headers:
      Content-Type: "application/json"
      X-Tsign-Open-App-Id: "${ENV(app_id)}"
    json:
      cert_name: "${'a' * 101}"  # 超过100字符限制
      phone: "${test_phone}"
      idcard: "${test_idcard}"
      algorithm: "SM2"
      cert_time: "ONEYEAR"
  validate:
  - eq: ["status_code", 400]
  - contains: ["content.message", "证书名称长度不能超过100字符"]

- name: "性能测试-响应时间验证"
  request:
    url: "/certificate/create"
    method: "POST"
    headers:
      Content-Type: "application/json"
      X-Tsign-Open-App-Id: "${ENV(app_id)}"
    json:
      cert_name: "${test_cert_name}_perf"
      phone: "${test_phone}"
      idcard: "${test_idcard}"
      algorithm: "SM2"
      cert_time: "ONEYEAR"
  validate:
  - eq: ["status_code", 200]
  - lt: ["elapsed.total_seconds", 5.0]  # 响应时间小于5秒

- name: "查询证书详情"
  request:
    url: "/certificate/query_detail"
    method: "POST"
    headers:
      Content-Type: "application/json"
      X-Tsign-Open-App-Id: "${ENV(app_id)}"
    json:
      cert_id: "$cert_id"
  validate:
  - eq: ["status_code", 200]
  - eq: ["content.status", "success"]
  - eq: ["content.data.cert_name", "${test_cert_name}"]
```

## 测试数据管理

### 环境变量配置
```yaml
# .env文件
base_url=http://localhost:8000
app_id=3438757422
tenant_id=b0f99abbc3cd4d63a5a2a84c452e52d6
```

### 测试数据参数化
```yaml
parameters:
  - cert_algorithms: ["SM2", "RSA", "ECC"]
  - cert_times: ["ONEYEAR", "TWOYEAR", "THREEYEAR"]
  - invalid_phones: ["", "123", "abc", "12345678901234567890"]
```

## 断言类型

### 状态码断言
```yaml
validate:
- eq: ["status_code", 200]  # 等于
- ne: ["status_code", 500]  # 不等于
- gt: ["status_code", 199]  # 大于
- lt: ["status_code", 300]  # 小于
```

### 响应内容断言
```yaml
validate:
- eq: ["content.status", "success"]           # 字段值相等
- contains: ["content.message", "成功"]        # 包含字符串
- startswith: ["content.data.cert_id", "cert_"] # 以...开头
- endswith: ["content.data.cert_id", "_001"]   # 以...结尾
- regex_match: ["content.phone", "^1[3-9]\\d{9}$"] # 正则匹配
```

### 性能断言
```yaml
validate:
- lt: ["elapsed.total_seconds", 3.0]  # 响应时间小于3秒
- gt: ["elapsed.total_seconds", 0.1]  # 响应时间大于0.1秒
```

## 数据提取

### 提取响应数据
```yaml
extract:
- cert_id: "content.data.cert_id"      # 提取证书ID
- flow_id: "content.data.flow_id"      # 提取流程ID
- token: "headers.Authorization"        # 提取请求头
```

### 使用提取的数据
```yaml
json:
  cert_id: "$cert_id"  # 使用提取的证书ID
  flow_id: "$flow_id"  # 使用提取的流程ID
```

## 测试套件组织

### 按功能模块组织
```
testcases/
├── certificate/
│   ├── test_create.yml
│   ├── test_query.yml
│   └── test_revoke.yml
├── signing/
│   ├── test_flow.yml
│   └── test_sign.yml
└── identity/
    ├── test_verify.yml
    └── test_upload.yml
```

### 按测试类型组织
```
testcases/
├── functional/
├── performance/
├── security/
└── integration/
```
