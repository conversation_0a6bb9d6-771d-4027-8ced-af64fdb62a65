# API接口生成参考文档

## 代码生成示例

### 服务层代码示例
```python
def create_certificate(cert_name: str, phone: str, idcard: str, 
                      algorithm: str = "SM2", cert_time: str = "ONEYEAR",
                      app_id: Optional[str] = None, environment: Optional[str] = None) -> Dict[str, Any]:
    """创建数字证书"""
    try:
        # 参数验证
        if not cert_name:
            return formatter.error(message="证书名称不能为空")
        if not phone:
            return formatter.error(message="手机号不能为空")
        if not idcard:
            return formatter.error(message="身份证号不能为空")
            
        # 构建请求参数
        params = {
            "certName": cert_name,
            "phone": phone,
            "idcard": idcard,
            "algorithm": algorithm,
            "certTime": cert_time
        }
        
        if app_id:
            params["appId"] = app_id
            
        # 调用API
        result = api_client.post("/certificate/create", params, environment)
        
        return formatter.success(
            data=result,
            message="证书创建成功"
        )
        
    except Exception as e:
        logger.error(f"创建证书失败: {str(e)}")
        return formatter.error(
            message=f"创建证书失败: {str(e)}"
        )
```

### 控制器代码示例
```python
@certificate_router.post(
    "/create",
    summary="🔐 创建证书",
    description="创建数字证书，支持多种算法和有效期",
    operation_id="certificate_create"
)
async def create_certificate_endpoint(
    cert_name: str,
    phone: str,
    idcard: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建数字证书"""
    return create_certificate(cert_name, phone, idcard, algorithm, cert_time, app_id, environment)
```

## 文件组织结构
```
mcpService/
├── domains/
│   ├── certificate/
│   │   ├── __init__.py
│   │   └── certificate_service.py
│   └── signing/
│       ├── __init__.py
│       └── signing_service.py
└── controllers/
    ├── certificate_controller.py
    └── signing_controller.py
```

## 命名规范
- 服务方法：动词_名词格式，如 create_certificate
- 控制器端点：方法名_endpoint格式
- 参数名：小写下划线格式
- 响应字段：驼峰格式
