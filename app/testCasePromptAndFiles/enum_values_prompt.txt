# 枚举值验证提示词

## 任务描述
生成针对API接口枚举参数的验证测试用例。

## 验证要求

### 1. 有效枚举值验证
- 所有有效枚举值
- 枚举值大小写
- 枚举值格式
- 枚举值组合

### 2. 无效枚举值验证
- 不存在的枚举值
- 错误格式的枚举值
- 空枚举值
- 特殊字符枚举值

### 3. 枚举值边界验证
- 枚举值长度限制
- 枚举值数量限制
- 枚举值范围限制
- 枚举值依赖关系

### 4. 枚举值业务验证
- 枚举值业务含义
- 枚举值处理逻辑
- 枚举值状态变化
- 枚举值权限控制

## 测试用例格式

```
TL-{参数名}传入有效枚举值{枚举值}验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：传入枚举值{枚举值}
步骤三：验证响应结果
ER-预期结果：1：接口调用成功；2：枚举值处理正确；3：业务逻辑正常；

TL-{参数名}传入无效枚举值验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：传入无效枚举值
步骤三：验证响应结果
ER-预期结果：1：返回参数错误；2：错误码为400；3：提示枚举值无效；
```

生成完整的枚举值验证测试用例。
