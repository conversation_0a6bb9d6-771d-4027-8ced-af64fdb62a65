"""我是一个资深软件测试专家，为你提供了一套完整的AI生成测试用例全流程提示词，请记住并理解提示词，后续我使用快速调用指令时，你必须按照以下步骤执行：

1. 立即开始需求信息收集（如信息不完整则自行mock补充）

2. 依次执行9个步骤的完整流程

3. 在第四步"测试用例生成及格式化"完成后，立即生成符合XMind导入标准的markdown文件

4. 文件生成完成后，继续执行后续的优化、评审、冒烟用例提取、线上验证用例提取步骤

5. 最终更新markdown文件，确保包含所有类型的测试用例

【文件生成时机】：在完成测试用例生成后立即生成文件，不等待用户要求

提示词的详细内容如下：

一、需求信息收集提示词

请提供以下需求信息，以便进行测试用例设计：

1. 需求基本信息

需求名称：[需求名称]
需求类型：[功能需求/性能需求/安全需求/其他]
需求优先级：[高/中/低]

2. 需求描述

业务背景：[描述需求的业务背景]
功能描述：[详细描述需求功能]
用户价值：[描述需求带来的价值]
验收标准：[描述需求的验收标准]

3. 功能范围

核心功能：[列出核心功能点]
功能模块：[列出相关功能模块]
功能依赖：[列出功能依赖关系]
功能限制：[列出功能限制条件]

4. 用户角色

主要角色：[列出主要用户角色]
角色权限：[描述各角色权限]
角色关系：[描述角色之间的关系]
特殊要求：[描述特殊角色要求]

5. 业务规则

基本规则：[列出基本业务规则]
验证规则：[列出数据验证规则]
处理规则：[列出业务处理规则]
异常规则：[列出异常处理规则]

6. 数据要求

数据实体：[列出相关数据实体]
数据关系：[描述数据之间的关系]
数据验证：[描述数据验证要求]
数据限制：[描述数据限制条件]

7. 界面要求

页面布局：[描述页面布局要求]
交互方式：[描述交互方式要求]
响应要求：[描述响应时间要求]
兼容要求：[描述兼容性要求]

8. 性能要求

响应时间：[描述响应时间要求]
并发要求：[描述并发处理要求]
资源限制：[描述资源使用限制]
性能指标：[描述具体性能指标]

9. 安全要求

权限控制：[描述权限控制要求]
数据安全：[描述数据安全要求]
访问控制：[描述访问控制要求]
安全审计：[描述安全审计要求]

10. 其他要求

特殊说明：[描述特殊说明事项]
注意事项：[描述需要注意的事项]
参考文档：[列出相关参考文档]
补充信息：[其他补充信息]

二、需求分析提示词

请基于收集的需求信息，进行需求分析，输出如下内容：

1. 功能模块：

- 核心模块：
- 辅助模块：
- 可选模块：
- 模块关系：

2. 核心功能点：

- 主要功能：
- 功能优先级：
- 功能依赖：
- 功能流程：

3. 用户角色：

- 主要用户：
- 次要用户：
- 管理用户：
- 角色交互：

4. 业务规则：

- 核心规则：
- 约束条件：
- 异常情况：
- 边界条件：

5. 数据实体：

- 主要实体：
- 实体属性：
- 实体关系：
- 数据流向：

三、测试场景分析提示词

请基于需求分析结果，请设计一下7中类型的测试场景：

1. 功能测试场景：

- 正常流程场景：
- 单功能验证场景：
- UI交互验证：
- 异常流程场景：
- 边界条件场景：

2. 功能闭环场景验证
- 以新/老用户视角的全流程闭环场景用例
- 多功能模块交叉组合场景用例

3. 边界测试场景：

- 输入边界：
- 数据边界：
- 时间边界：

4. 异常测试场景：
- 错误输入：
- 系统异常：
- 网络异常：

5. 性能测试场景：

- 响应时间：
- 并发性能：
- 压力测试：

6. 安全测试场景：

- 权限验证：
- 数据安全：
- 接口安全：

7. 兼容性测试场景：

- 多端验证：PC、H5、客户端、微信小程序、支付宝小程序
- 多设备验证：主流设备型号

四、测试用例生成及格式化提示

基于测试场景分析，请按照以下格式直接生成最终可用的测试用例：

4.1 测试用例格式标准

【结构要求】：
每个用例采用独立结构，使用#缩进表示层级：

TL-用例标题
PD-前置条件：前置条件1；前置条件2；
步骤一：操作描述
步骤二：操作描述
步骤三：操作描述
ER-预期结果1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

MYTL-冒烟用例标题
PD-前置条件：前置条件1；前置条件2；
步骤一：操作描述
步骤二：操作描述
步骤三：操作描述
ER-预期结果1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

PATL-线上验证用例标题
PD-前置条件：前置条件1；前置条件2；
步骤一：操作描述
步骤二：操作描述
步骤三：操作描述
ER-预期结果1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

【格式要求】：
- 任何地方都不使用"*"号
- 使用#缩进表示层级关系
- 每个用例完全独立，不混合在一起
- 中文数字编号（步骤一、步骤二）
- 阿拉伯数字结果编号（1、2、3、4、5）

4.3 用例生成范围

默认生成： 功能、边界、异常、性能、安全、兼容性等所有场景的测试用例
指定生成： 可根据用户指定的场景类型生成对应用例
示例1："生成功能测试场景的测试用例" → 仅生成功能测试场景用例
示例2："生成功能和安全测试场景的测试用例" → 生成功能+安全场景用例

4.3 用例生成要求

- 每类场景的测试用例不少于3个
- 覆盖所有核心功能点
- 测试步骤详细具体
- 期望结果明确可验证
- 直接输出最终格式，无需后续格式化

五、测试用例优化提示词

5.1请从以下维度优化测试用例：

5.1.1 完整性检查

- 功能覆盖完整性
- 场景覆盖完整性
- 数据覆盖完整性

5.1.2 准确性检查

- 步骤描述准确性
- 期望结果准确性
- 测试数据准确性

5.1.3 可执行性检查

- 步骤可操作性
- 前置条件可达成性
- 测试环境可用性

5.1.4 可维护性检查

- 用例独立性
- 步骤清晰性
- 数据可重用性

5.2 优化后的输出

输出优化后的测试用例，保持标准格式。

六、测试用例评审及补充遗漏场景提示词

6.1 评审标准

请基于测试用例进行评审，检查：

6.1.1需求覆盖度检查

-所有功能需求已覆盖
-所有非功能需求已覆盖
-业务规则已体现
-异常场景已包含
-所有功能需求已有闭环验证场景覆盖
-场景交叉组合已覆盖

6.1.2 测试设计检查
- 正常流程完整
- 异常流程充分
- 边界条件合理
- 测试数据充足

6.1.3 用例质量检查
- 步骤描述清晰
- 期望结果明确
- 前置条件完整

6.1.4 格式规范检查
- 编号规范统一
- 格式符合标准
- 步骤编号正确

6.1.5缺口识别

列出发现的测试缺口：
- 功能缺口：
- 场景缺口：
- 数据缺口：

6.2 评审结果输出格式

评审结果：
1. 需求覆盖度：[百分比]
2. 场景完整性：[百分比]
3. 步骤合理性：[百分比]
4. 结果可验证性：[百分比]
5. 数据充分性：[百分比]
6. 遗漏场景：[具体场景列表]

6.3 补充测试用例

如存在遗漏场景，将遗漏场景按照标准格式生成测试用例。

七、冒烟测试用例提取提示词

请从完整测试用例中提取冒烟测试用例：

7.1 冒烟用例提取要求

覆盖核心功能点
验证基本业务流程
确保关键功能可用
用例数量控制在总用例的30%

7.2 冒烟用例输出格式

MYTL-冒烟用例标题
PD-前置条件：前置条件1；前置条件2；
步骤一：操作描述
步骤二：操作描述
步骤三：操作描述
ER-预期结果
1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

7.3 数量校验

对输出的冒烟用例进行数量检查：
公式：冒烟用例数量 = 总用例数 × 30%
示例：总用例20条，冒烟用例应为6条左右

八、线上验证用例提取提示词

请从完整测试用例中提取线上验证用例：

8.1 线上验证用例提取要求

覆盖需求发布的所有功能点
验证业务全流程可用
确保能验证到完整的功能需求
用例数量控制在总用例的30%以下

8.2 线上验证用例输出格式
线上验证用例：
PATL-线上验证用例标题
PD-前置条件：前置条件1；前置条件2；
步骤一：操作描述
步骤二：操作描述
步骤三：操作描述
ER-预期结果
1：预期结果描述1
2：预期结果描述2
3：预期结果描述3

8.3 数量校验

对输出的线上验证用例进行数量检查：
公式：线上验证用例数量 = 总用例数 × 30%
示例：总用例20条，线上验证用例应为6条左右

九、生成符合XMind导入标准的markdown文件

要求：
1. 立即生成符合XMind导入标准的markdown文件
3. 包含完整的8类测试用例，用例数量完整，
4. 重点关注[具体场景]的详细测试
5. 结构化组织原则：
- 多需求场景：每个需求作为一级分类，下设功能模块和测试类型
- 功能模块场景：功能模块作为一级分类，下设测试类型
- 混合场景：需求→功能模块→测试类型→用例标题→前置条件、操作步骤、预期结果的多级结构
- 必须保证每条用例作为单独的分支存在，不要混在一起，可读性太差满足，需求→功能模块→测试类型→用例标题→前置条件、操作步骤、预期结果的多级结构
- markdown格式参考：

# 【V3api】签署接口指定实名认证的证件类型-测试用例

## 功能测试

### 证件类型处理逻辑

#### TL-创建签署流程时psnIDCardNum为空psnIDCardType不为空透传验证

##### PD-前置条件：具有创建签署流程权限、

##### 步骤一：调用V3创建签署流程接口

##### 步骤二：设置psnIDCardNum为空

##### 步骤三：设置psnIDCardType为CRED_PSN_CH_TWCARD

##### 步骤四：提交创建请求

##### 步骤五：进入实名认证页面

##### ER-预期结果: 1：签署流程创建成功； 2：实名认证页面展示证件类型为"台湾来往大陆通行证"；3：证件类型字段不可修改；4：certType成功透传到实名认证系统；

#### TL-创建签署流程时psnIDCardNum不为空psnIDCardType为空默认值设置验证

##### PD-前置条件：用户已登录；具有创建签署流程权限；实名认证服务正常；

##### 步骤一：调用V3创建签署流程接口

##### 步骤二：设置psnIDCardNum为有效身份证号

##### 步骤三：设置psnIDCardType为空

##### 步骤四：提交创建请求

##### 步骤五：检查系统处理结果

##### ER-预期结果：1：签署流程创建成功；2：psnIDCardType自动设置为CRED_PSN_CH_IDCARD；3：实名认证页面展示证件类型为"中华人民共和国居民身份证"；4：证件类型字段不可修改；

请按照以上完整流程执行测试用例生成任务。"""
