# 必填枚举参数验证参考文档

## 测试用例示例

### 证书算法必填枚举参数验证

#### TL-必填枚举参数algorithm传入有效值SM2验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入必填枚举参数algorithm为SM2
##### 步骤三：验证响应结果
##### ER-预期结果：1：接口调用成功；2：参数验证通过；3：证书算法设置正确；

#### TL-必填枚举参数algorithm为空验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：必填枚举参数algorithm传空值
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示算法类型不能为空；

#### TL-必填枚举参数algorithm传入无效枚举值验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入无效枚举值INVALID_ALGORITHM
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示算法类型无效；

### 证书有效期必填枚举参数验证

#### TL-必填枚举参数certTime传入有效值ONEYEAR验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入必填枚举参数certTime为ONEYEAR
##### 步骤三：验证响应结果
##### ER-预期结果：1：接口调用成功；2：参数验证通过；3：证书有效期设置正确；

#### TL-必填枚举参数certTime未传递验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：不传递certTime参数
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示证书有效期不能为空；

## 常见必填枚举参数

### 证书服务
- algorithm (算法类型): SM2, RSA, ECC - 必填
- certTime (有效期): ONEYEAR, TWOYEAR, THREEYEAR - 必填
- certType (证书类型): PERSONAL, ENTERPRISE - 必填

### 签署服务
- signType (签署类型): SINGLE, BATCH, FLOW - 必填
- verifyType (验证方式): SMS, FACE, PASSWORD - 必填
- docType (文档类型): PDF, WORD, IMAGE - 必填

### 认证服务
- authType (认证类型): BASIC, ADVANCED, QUALIFIED - 必填
- idCardType (证件类型): IDCARD, PASSPORT, DRIVING_LICENSE - 必填
- verifyMethod (验证方式): MANUAL, AUTO, HYBRID - 必填

## 验证优先级

### 高优先级验证
1. 参数缺失验证 - 确保必填参数存在
2. 参数为空验证 - 确保参数有值
3. 有效枚举值验证 - 确保业务正常流程

### 中优先级验证
1. 无效枚举值验证 - 确保错误处理正确
2. 枚举值大小写验证 - 确保格式要求
3. 枚举值组合验证 - 确保参数依赖关系

### 低优先级验证
1. 特殊字符验证 - 确保安全性
2. 超长字符串验证 - 确保稳定性
3. 边界值验证 - 确保健壮性

## 错误处理标准

### 参数缺失错误
- 错误码: 400
- 错误信息: "{参数名}不能为空"
- 错误类型: PARAMETER_MISSING

### 枚举值无效错误
- 错误码: 400
- 错误信息: "{参数名}值无效，支持的值: {枚举列表}"
- 错误类型: PARAMETER_INVALID

### 参数格式错误
- 错误码: 400
- 错误信息: "{参数名}格式错误"
- 错误类型: PARAMETER_FORMAT_ERROR
