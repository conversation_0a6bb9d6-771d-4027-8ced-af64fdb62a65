# 必填参数验证提示词

## 任务描述
生成针对API接口必填参数的验证测试用例。

## 验证要求

### 1. 参数缺失验证
- 必填参数为空
- 必填参数未传递
- 必填参数为null
- 必填参数为空字符串

### 2. 参数格式验证
- 参数类型错误
- 参数长度限制
- 参数格式要求
- 特殊字符处理

### 3. 参数值验证
- 参数值范围
- 参数值枚举
- 参数值依赖
- 参数值组合

### 4. 错误响应验证
- 错误码正确性
- 错误信息准确性
- 错误提示友好性
- 错误处理完整性

## 测试用例格式

```
TL-{参数名}为空时的验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：传入空的{参数名}
步骤三：验证响应结果
ER-预期结果：1：返回参数错误；2：错误码为400；3：提示{参数名}不能为空；
```

生成完整的必填参数验证测试用例。
