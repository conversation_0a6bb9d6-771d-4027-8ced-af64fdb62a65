# 枚举值验证参考文档

## 测试用例示例

### 证书算法枚举验证

#### TL-算法参数传入有效枚举值SM2验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入算法参数SM2
##### 步骤三：验证响应结果
##### ER-预期结果：1：接口调用成功；2：证书算法设置为SM2；3：证书创建成功；

#### TL-算法参数传入有效枚举值RSA验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入算法参数RSA
##### 步骤三：验证响应结果
##### ER-预期结果：1：接口调用成功；2：证书算法设置为RSA；3：证书创建成功；

#### TL-算法参数传入无效枚举值验证
##### PD-前置条件：用户已登录；具有证书创建权限；其他参数有效；
##### 步骤一：调用证书创建接口
##### 步骤二：传入无效算法参数INVALID
##### 步骤三：验证响应结果
##### ER-预期结果：1：返回参数错误；2：错误码为400；3：提示算法类型无效；

## 常见枚举类型

### 证书相关枚举
- 算法类型：SM2, RSA, ECC
- 证书有效期：ONEYEAR, TWOYEAR, THREEYEAR
- 证书状态：VALID, INVALID, REVOKED
- 证书类型：PERSONAL, ENTERPRISE

### 签署相关枚举
- 签署类型：SINGLE, BATCH, FLOW
- 签署状态：PENDING, SIGNED, CANCELLED
- 签署方式：SMS, FACE, PASSWORD
- 文档类型：PDF, WORD, IMAGE

### 认证相关枚举
- 认证类型：BASIC, ADVANCED, QUALIFIED
- 认证状态：SUCCESS, FAILED, PENDING
- 证件类型：IDCARD, PASSPORT, DRIVING_LICENSE
- 认证方式：MANUAL, AUTO, HYBRID

### 环境相关枚举
- 环境类型：DEV, TEST, STAGING, PROD
- 地区代码：CN, US, EU, APAC
- 语言代码：zh-CN, en-US, ja-JP

## 验证策略

### 正向验证
- 遍历所有有效枚举值
- 验证每个枚举值的业务处理
- 检查枚举值的大小写敏感性
- 确认枚举值的默认行为

### 负向验证
- 传入不存在的枚举值
- 传入空枚举值
- 传入特殊字符
- 传入超长字符串

### 边界验证
- 枚举值组合测试
- 枚举值依赖关系测试
- 枚举值权限验证
- 枚举值状态变化测试
