# API接口生成提示词

## 任务描述
根据提供的API文档、curl命令或接口描述，生成标准的MCP服务代码。

## 生成要求

### 1. 代码结构
- 生成服务层代码（service层）
- 生成控制器代码（controller层）
- 包含完整的错误处理
- 支持环境切换

### 2. 参数处理
- 必填参数验证
- 可选参数默认值
- 参数类型转换
- 参数格式验证

### 3. 响应处理
- 统一响应格式
- 错误信息处理
- 状态码映射
- 数据格式化

### 4. 文档生成
- 接口描述
- 参数说明
- 响应示例
- 错误码说明

## 输出格式

### 服务层代码
```python
def method_name(param1: str, param2: Optional[str] = None, environment: Optional[str] = None) -> Dict[str, Any]:
    """方法描述"""
    try:
        # 参数验证
        # API调用
        # 响应处理
        return formatter.success(data=result, message="操作成功")
    except Exception as e:
        return formatter.error(message=f"操作失败: {str(e)}")
```

### 控制器代码
```python
@router.post("/endpoint", summary="接口描述")
async def endpoint(param1: str, param2: Optional[str] = None):
    """接口描述"""
    return method_name(param1, param2)
```

生成完整的可用代码。
