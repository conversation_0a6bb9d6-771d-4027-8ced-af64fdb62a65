# HttpRunner测试用例生成提示词

## 任务描述
生成标准的HttpRunner格式测试用例，用于API接口的自动化测试。

## 生成要求

### 1. 测试用例结构
- 使用标准的HttpRunner YAML格式
- 包含完整的测试配置
- 支持参数化和数据驱动

### 2. 测试场景覆盖
- 正常流程测试
- 异常场景测试
- 边界值测试
- 性能基准测试

### 3. 数据管理
- 测试数据参数化
- 环境变量配置
- 动态数据生成
- 数据清理策略

### 4. 断言验证
- 响应状态码验证
- 响应体内容验证
- 响应头验证
- 性能指标验证

### 5. 依赖管理
- 前置条件设置
- 测试数据准备
- 环境依赖检查
- 后置清理操作

## 输出格式
```yaml
name: "API测试用例"
config:
    name: "测试配置"
    base_url: "${ENV(base_url)}"
    variables:
        user_agent: "HttpRunner/3.0"
    verify: false

teststeps:
- name: "测试步骤名称"
  request:
    url: "/api/endpoint"
    method: "POST"
    headers:
      Content-Type: "application/json"
    json:
      param1: "${param1}"
      param2: "${param2}"
  validate:
  - eq: ["status_code", 200]
  - eq: ["content.success", true]
```

生成完整的HttpRunner测试用例文件。
