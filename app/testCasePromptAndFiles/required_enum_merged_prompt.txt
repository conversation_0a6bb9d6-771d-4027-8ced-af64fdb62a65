# 必填参数与枚举值合并验证提示词

## 任务描述
生成针对API接口中既是必填又是枚举类型参数的综合验证测试用例。

## 验证要求

### 1. 必填枚举参数验证
- 参数缺失验证
- 参数为空验证
- 参数格式验证
- 枚举值有效性验证

### 2. 综合场景验证
- 必填+有效枚举值
- 必填+无效枚举值
- 必填+空枚举值
- 必填+格式错误枚举值

### 3. 业务逻辑验证
- 枚举值对应的业务处理
- 枚举值状态变化
- 枚举值权限验证
- 枚举值依赖关系

### 4. 错误处理验证
- 参数缺失错误
- 枚举值无效错误
- 格式错误处理
- 业务逻辑错误

## 测试用例格式

```
TL-必填枚举参数{参数名}传入有效值{枚举值}验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：传入必填枚举参数{枚举值}
步骤三：验证响应结果
ER-预期结果：1：接口调用成功；2：参数验证通过；3：业务处理正确；

TL-必填枚举参数{参数名}为空验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：必填枚举参数传空值
步骤三：验证响应结果
ER-预期结果：1：返回参数错误；2：错误码为400；3：提示参数不能为空；

TL-必填枚举参数{参数名}传入无效枚举值验证
PD-前置条件：接口正常；其他参数有效；
步骤一：调用接口
步骤二：传入无效枚举值
步骤三：验证响应结果
ER-预期结果：1：返回参数错误；2：错误码为400；3：提示枚举值无效；
```

生成完整的必填枚举参数验证测试用例。
