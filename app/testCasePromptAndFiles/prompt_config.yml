prompt_types:
  接口生成提示词:
    description: "根据API文档生成接口代码的提示词"
    file: "api_generation_prompt.txt"
    reference_file: "api_generation_reference.md"
    
  必填参数提示词:
    description: "生成必填参数验证的提示词"
    file: "required_params_prompt.txt"
    reference_file: "required_params_reference.md"
    
  枚举值提示词:
    description: "生成枚举值验证的提示词"
    file: "enum_values_prompt.txt"
    reference_file: "enum_values_reference.md"
    
  必填与枚举合并提示词:
    description: "生成必填参数和枚举值合并验证的提示词"
    file: "required_enum_merged_prompt.txt"
    reference_file: "required_enum_merged_reference.md"
    
  通用HttpRunner测试用例生成提示词:
    description: "生成HttpRunner格式测试用例的提示词"
    file: "httprunner_testcase_prompt.txt"
    reference_file: "httprunner_testcase_reference.md"
    
  完整集成测试用例生成提示词:
    description: "生成完整集成测试用例的全流程提示词"
    file: "complete_testcase_generation_prompt.txt"

  HttpRunner自动化用例生成提示词:
    description: "生成HttpRunner自动化测试用例的专业提示词"
    file: "httprunner_automation_prompt.txt"

  简版测试用例生成提示词:
    description: "简化版测试用例生成AI提示词，快速生成标准格式测试用例"
    file: "simple_testcase_generation_prompt.txt"
