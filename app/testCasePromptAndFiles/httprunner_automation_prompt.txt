我是一个资深软件测试专家，为你提供了一套完整的AI生成HttpRunner自动化用例提示词，请记住并理解提示词，后续我使用快速调用指令时，请按此模板执行。执行完成后，自动帮我生成脚本文件

提示词的详细内容如下：

一、API信息收集
1.1 接口基础信息

| 字段 | 内容（请补充） |
|------------|------------------------------|
| 接口名称 | |
| 接口描述 | |
| 所属模块 | |
| 请求方式 | GET/POST/PUT/DELETE |
| 基础URL | |
| 接口路径 | |
| 认证方式 | Bearer Token/其他 |
| 请求头 | Authorization、Content-Type等 |

示例：

| 字段 | 内容 |
|------------|------------------------------|
| 接口名称 | 合同模板列表查询 |
| 接口描述 | 查询已创建的合同模板列表 |
| 所属模块 | 合同模板管理 |
| 请求方式 | GET |
| 基础URL | http://api-test-as1.esignglobal-inc.com |
| 接口路径 | /esignglobal/v1/templates/templateList |
| 认证方式 | Bearer Token |
| 请求头 | Authorization、X-Tsign-Service-Group、accept |

1.2 请求参数

[请按以下格式提供参数信息]
参数名称 | 是否必填 | 参数类型 | 参数说明 | 示例值
---------|----------|----------|----------|--------
[参数1] | 是/否 | string/int/boolean | [参数说明] | [示例值]
[参数2] | 是/否 | string/int/boolean | [参数说明] | [示例值]

示例：

| 参数名 | 是否必填 | 类型 | 默认值 | 参数说明 | 示例值 |
|----------|----------|--------|--------|----------------------|--------|
| pageNum | 否 | int | 1 | 查询页码 | 1 |
| pageSize | 否 | int | 20 | 每页显示的数量,最大20 | 10 |

1.3 响应参数

| 参数名 | 参数类型 | 是否必返 | 参数说明 | 示例值 |
|--------|----------|----------|----------|---------|
| [参数1] | [类型] | 是/否 | [说明] | [示例] |
| [参数2] | [类型] | 是/否 | [说明] | [示例] |

示例：

| 参数名 | 类型 | 是否必返 | 参数说明 | 示例值 |
|-------------------------------|---------|----------|--------------------------------------------|------------------------------|
| code | string | 是 | 业务状态码 | 0 |
| message | string | 是 | 响应消息 | success |
| success | boolean | 是 | 是否成功 | true |
| data.total | int | 是 | 查询结果中模板的总数量 | 32 |
| data.templateList | array | 是 | 合同模板列表 | - |
| data.templateList[].templateId| string | 是 | 合同模板ID | d9147dfbd1b24ca6bd66e7b3e2d65481 |
| data.templateList[].templateName | string| 是 | 合同模板名称 | 表格-word |
| data.templateList[].templateType | int | 是 | 合同模板类型:0-PDF,1-WORD | 1 |
| data.templateList[].createTime| string | 是 | 创建时间(毫秒时间戳) | 1747704717119 |
| data.templateList[].updateTime| string | 是 | 更新时间(毫秒时间戳) | 1747916180683 |
| data.templateList[].templateStatus | int | 是 | 模板状态:0-未启用,1-启用 | 1 |
| data.templateList[].owner | string | 是 | 模板拥有者 | binglan1 |
| data.templateList[].tags | string | 是 | 标签信息,多个标签以","隔开 | |

二、测试用例场景设计
1. 正常场景

- 场景描述: [正常业务流程描述]
- 测试数据: [有效测试数据]
- 验证重点: [关键验证点]

2. 异常场景
- 参数缺失场景: [描述缺少必填参数的情况]
- 参数错误场景: [描述参数类型或格式错误的情况]
- 业务异常场景: [描述违反业务规则的情况]

3.串业务场景

描述：接口A提取数据作为接口B的入参，验证数据流转

三、用例生成

3.1.单接口的测试用例生成
请基于已收集到的api信息，生成单接口的HttpRunner场景测试用例,请确保生成的YAML格式正确，可以直接在HttpRunner中执行。

3.1.1 测试场景要求:

正常场景：使用有效参数的正常业务流程测试
异常场景：
必填参数缺失测试
参数格式错误测试
认证失败测试（如适用）
业务规则违反测试

3.1.2 请生成完整的HttpRunner YAML格式测试用例，包含：

注意点：

API接口文件生成，要求如下
一：如果带有query的请求类型，则在url后拼接?$params，不需要指定key，只需要指定一个?$params，如果没有query的请求类型，则不用添加
二：读取请求方式，并修改method
三：如果存在body的请求类型，则需要设置参数json，参数化为$json，不需要细化到里面的各个参数
四：若存在参数化，注意是否遗漏了$符号
五：检查时注意层级和缩进，必须和模板保持一致
六：文件名根据接口地址拆分，不需要联想
七：没有特别说明的情况下，不要增删内容
八：创建的文件路径不需要带有{}这个大括号，如果path中带有参数化的话，直接把这个key作为文件路径

格式如下:
request:
    url: [实际的API路径，如/openca/rest/cert/createnew]
    method: [HTTP方法]
    headers: [请求头配置]
    json: $json
validate:
- eq: ["status_code", 200]

示范：

request:
url: /v4/signflows/batchSign/${batchSerialId}/batchSign
method: POST
headers: ${gen_headers($app_Id)}
json: $json
validate:
- eq: ["status_code", 200]

测试步骤文件生成规则

- config:
    name: [从场景描述生成的测试名称]
    base_url: ${ENV(base_url)}
    variables: [全局变量，步骤公共变量]

- test:
    name: [步骤名称]
    api: [对应的API接口文件]
    variables: [JSON格式参数化的API请求body体，query请求参数params使用"params1=1&params=2"这种格式生成字符串，步骤变量]
    extract: [需要提取的数据]
    validate: [验证规则]

示范：

- config:
name: 3.0主流程场景
base_url: ${ENV(openapi_url)}
variables:
- app_Id: ${ENV(app_Id)}
- app_secret: ${ENV(app_Id_secret_saas_realname_willing)}
- file_id: ${ENV(file_id_in_app_Id_saas_realname_willing)}
- psn_id: ${ENV(psn_1)}
- psn_seal: ${ENV(psn_1_seal)}

- test:
name: 3.0轩辕#rsa#手绘+企业+手动+部分审批#可信签
variables:
- json: {"docs":[{"fileId":"${file_id}"}],"signFlowConfig":{"signFlowTitle":"3.0主流程场景"},"signers":[{"psnSignerInfo":{"psnId":"${psn_id}"}}]}
api: api/sign/signflows/create-by-file.yml
extract:
- signFlowId: content.data.signFlowId
validate:
- eq: ["status_code", 200]
- eq: ["content.code", 0]
- eq: ["content.message", "成功"]

- test:
name: 3.0轩辕#rsa#手绘+企业+手动+部分审批#可信签
variables:
- params: "a=1&b=2"
api: api/sign/signflows/create-by-file.yml
extract:
- signFlowId: content.data.signFlowId
validate:
- eq: ["status_code", 200]
- eq: ["content.code", 0]
- eq: ["content.message", "成功"]

3.完整的验证规则：

HTTP状态码验证
业务状态码验证
关键字段验证

4.数据提取配置：

如需要在后续步骤中使用

5.清晰的用例命名和注释

3.2.业务流程场景的测试用例生成
请为以下业务流程生成HttpRunner场景测试用例,请确保生成的场景测试能够完整验证业务流程的正确性和数据一致性。

3.2.1业务流程信息
- 流程名称：[如：用户注册到登录完整流程]
- 流程描述：[详细描述整个业务流程的步骤]
- 业务价值：[说明这个流程的业务意义]

3.2.2涉及接口清单
[按执行顺序列出所有接口]
1. 接口名称：[如：用户注册] | 方法：POST | 路径：/api/users/register
2. 接口名称：[如：用户登录] | 方法：POST | 路径：/api/users/login
3. 接口名称：[如：获取用户信息] | 方法：GET | 路径：/api/users/{id}
[继续添加其他接口...]

3.2.3测试要求
1. 实现完整业务流程的自动化测试
2. 正确处理接口间的数据传递和依赖
3. 每个步骤都要有充分的验证
4. 考虑流程中断和异常情况的处理
5. 添加关键业务节点的数据验证

3.2.4输出要求
生成完整的HttpRunner场景测试YAML文件到对应的项目文件夹中，包含：
- 统一的config配置
- 按业务流程顺序排列的teststeps
- 合理的数据提取和变量传递
- 每个步骤的详细验证规则
- 异常情况的处理和验证
- 清晰的步骤命名和注释

四、覆盖率检查
| 检查项 | 是否覆盖 | 说明/备注 |
|----------------|----------|---------------------|
| 正常流程 | 是/否 | |
| 必填参数缺失 | 是/否 | |
| 参数类型错误 | 是/否 | |
| 参数越界 | 是/否 | |
| 无权限/未登录 | 是/否 | |
| 串场景 | 是/否 | |
| 关键字段校验 | 是/否 | |
| 响应结构校验 | 是/否 | |
| 业务规则校验 | 是/否 | |

示例：

| 检查项 | 是否覆盖 | 说明/备注 |
|----------------|----------|---------------------|
| 正常流程 | 是 | 查询列表 |
| 必填参数缺失 | 是 | token缺失 |
| 参数类型错误 | 是 | pageNum传string |
| 参数越界 | 是 | pageSize=21 |
| 无权限/未登录 | 是 | 缺少token |
| 串场景 | 是 | 列表-详情 |
| 关键字段校验 | 是 | templateId等 |
| 响应结构校验 | 是 | type_match等 |
| 业务规则校验 | 是 | 状态、类型等 |
