#!/usr/bin/env python3
"""
证书域控制器 - 数字证书相关功能
提供证书创建、查询、更新、吊销等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.certificate_service import (
    get_test_account, create_certificate, query_certificate_detail, 
    get_user_info, revoke_certificate, update_certificate
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建证书域路由器
certificate_router = APIRouter(
    prefix="/certificate",
    tags=["证书域"],
    responses={404: {"description": "Not found"}}
)


@certificate_router.post(
    "/get_test_account",
    summary="📱 获取测试账号",
    description="获取测试账号信息，包括手机号、证件号、姓名等，支持环境切换",
    operation_id="certificate_get_test_account"
)
async def get_test_account_endpoint(
    environment: Optional[str] = Query(None, description="环境类型，支持自然语言描述")
):
    """
    获取测试账号信息
    
    支持环境切换：
    - 默认使用测试环境
    - 如果描述中包含"模拟"、"mock"等关键词，则使用模拟环境
    
    返回示例:
    {
      "status": "success",
      "data": {
        "phone": "***********",
        "idcard": "341321196102220130", 
        "name": "测试东广政",
        "idcardType": "IDCARD",
        "orgCode": "91000000QLRGC4C61Q",
        "englishName": "Clarence Chad Price",
        "bankCard": "****************",
        "orgName": "esigntest东广政经营的个体工商户"
      },
      "environment": "test"
    }
    """
    return get_test_account(environment)


@certificate_router.post(
    "/create_certificate",
    summary="🔐 创建证书",
    description="创建数字证书，支持多种算法和有效期",
    operation_id="certificate_create"
)
async def create_certificate_endpoint(
    cert_name: str,
    phone: str,
    idcard: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """
    创建数字证书
    
    参数说明:
    - cert_name: 证书姓名
    - phone: 手机号
    - idcard: 身份证号
    - algorithm: 加密算法，默认SM2
    - cert_time: 证书有效期，默认一年
    - app_id: 应用ID，不传则使用环境默认值
    - environment: 环境描述，支持自然语言
    """
    return create_certificate(
        cert_name, phone, idcard, algorithm, cert_time, app_id, environment
    )


@certificate_router.post(
    "/query_certificate_detail",
    summary="🔍 查询证书详情",
    description="查询证书详情信息",
    operation_id="certificate_query_detail"
)
async def query_certificate_detail_endpoint(
    cert_id: str,
    app_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询证书详情"""
    return query_certificate_detail(cert_id, app_id, environment)


@certificate_router.post(
    "/revoke_certificate",
    summary="❌ 吊销证书",
    description="吊销数字证书",
    operation_id="certificate_revoke"
)
async def revoke_certificate_endpoint(
    cert_info_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """吊销数字证书"""
    return revoke_certificate(cert_info_id, environment)


@certificate_router.post(
    "/update_certificate",
    summary="✏️ 更新证书",
    description="更新数字证书信息",
    operation_id="certificate_update"
)
async def update_certificate_endpoint(
    cert_info_id: str,
    cert_name: str,
    mobile: str,
    license_number: str,
    algorithm: str = "SM2",
    cert_time: str = "ONEYEAR",
    config_id: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """更新数字证书"""
    return update_certificate(
        cert_info_id, cert_name, mobile, license_number, 
        algorithm, cert_time, config_id, environment
    )


@certificate_router.post(
    "/get_user_info",
    summary="👤 获取用户信息",
    description="获取用户信息",
    operation_id="certificate_get_user_info"
)
async def get_user_info_endpoint(
    user_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """获取用户信息"""
    return get_user_info(user_id, environment)

# 导出router供main.py使用
router = certificate_router
