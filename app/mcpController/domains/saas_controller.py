#!/usr/bin/env python3
"""
SaaS域控制器 - SaaS平台相关功能
提供账号管理、组织管理等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.saas_service import (
    register_test_person_account, register_test_company_account,
    create_organization, add_member_to_org, query_org_info
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建SaaS域路由器
saas_router = APIRouter(
    prefix="/saas",
    tags=["SaaS域"],
    responses={404: {"description": "Not found"}}
)


@saas_router.post(
    "/register_test_person_account",
    summary="👤 注册测试个人账号",
    description="在e签宝注册一个个人测试账号",
    operation_id="saas_register_person_account"
)
async def register_test_person_account_endpoint(
    app_id: str,
    idNo: str,
    mobile: str,
    name: str,
    thirdPartyUserId: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """注册测试个人账号"""
    return register_test_person_account(
        app_id, idNo, mobile, name, thirdPartyUserId, environment
    )


@saas_router.post(
    "/register_test_company_account",
    summary="🏢 注册测试企业账号",
    description="在e签宝注册一个企业测试账号",
    operation_id="saas_register_company_account"
)
async def register_test_company_account_endpoint(
    app_id: str,
    idNumber: str,
    mobile: str,
    name: str,
    thirdPartyUserId: str,
    orgLegalIdNumber: str,
    orgLegalName: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """注册测试企业账号"""
    return register_test_company_account(
        app_id, idNumber, mobile, name, thirdPartyUserId,
        orgLegalIdNumber, orgLegalName, environment
    )


@saas_router.post(
    "/create_organization",
    summary="🏛️ 创建组织",
    description="创建测试组织",
    operation_id="saas_create_organization"
)
async def create_organization_endpoint(
    org_name: str,
    org_code: str,
    legal_name: str,
    legal_idcard: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建组织"""
    return create_organization(org_name, org_code, legal_name, legal_idcard, environment)


@saas_router.post(
    "/add_member_to_org",
    summary="👥 添加组织成员",
    description="向组织添加成员",
    operation_id="saas_add_member_to_org"
)
async def add_member_to_org_endpoint(
    org_id: str,
    member_name: str,
    member_mobile: str,
    member_idcard: str,
    role: str = "member",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """添加组织成员"""
    return add_member_to_org(
        org_id, member_name, member_mobile, member_idcard, role, environment
    )


@saas_router.post(
    "/query_org_info",
    summary="🔍 查询组织信息",
    description="查询组织详细信息",
    operation_id="saas_query_org_info"
)
async def query_org_info_endpoint(
    org_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询组织信息"""
    return query_org_info(org_id, environment)

# 导出router供main.py使用
router = saas_router
