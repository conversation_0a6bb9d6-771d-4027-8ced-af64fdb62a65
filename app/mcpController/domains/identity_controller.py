#!/usr/bin/env python3
"""
实名域控制器 - 实名认证相关功能
提供实名认证、身份验证等造数功能
"""
from fastapi import APIRouter, Query
import logging
from typing import Optional

from mcpService.domains.identity_service import (
    create_identity_verification, query_verification_status,
    upload_identity_materials, verify_bank_card, verify_mobile
)

# 配置日志
logger = logging.getLogger(__name__)

# 创建实名域路由器
identity_router = APIRouter(
    prefix="/identity",
    tags=["实名域"],
    responses={404: {"description": "Not found"}}
)


@identity_router.post(
    "/create_identity_verification",
    summary="🆔 创建实名认证",
    description="创建实名认证流程",
    operation_id="identity_create_verification"
)
async def create_identity_verification_endpoint(
    name: str,
    idcard: str,
    mobile: str,
    verification_type: str = "BASIC",
    environment: Optional[str] = Query(None, description="环境类型")
):
    """创建实名认证"""
    return create_identity_verification(
        name, idcard, mobile, verification_type, environment
    )


@identity_router.post(
    "/query_verification_status",
    summary="📊 查询认证状态",
    description="查询实名认证状态",
    operation_id="identity_query_verification_status"
)
async def query_verification_status_endpoint(
    verification_id: str,
    environment: Optional[str] = Query(None, description="环境类型")
):
    """查询认证状态"""
    return query_verification_status(verification_id, environment)

# 导出router供main.py使用
router = identity_router