#!/usr/bin/env python3
"""
平台控制器 - 造数平台通用功能
提供环境管理、代码生成、提示词管理等平台级功能
"""
from fastapi import APIRouter, Body, Query, File, UploadFile
import logging
from typing import Optional, Dict, Any, List

from mcpService.platform.prompt_service import (
    get_prompt_types, get_prompt_content, search_prompts,
    get_接口生成提示词, get_必填参数提示词, get_枚举值提示词,
    get_必填与枚举合并提示词, get_通用HttpRunner测试用例生成提示词,
    get_全套集测提示词, get_测试用例生成提示词, get_HttpRunner自动化用例生成提示词,
    get_简版测试用例生成提示词
)
from mcpService.platform.code_generator import generate_mcp_code_from_curl
from mcpService.platform.environment_service import get_environment_info, switch_environment
# 删除的监控和版本服务

# 配置日志
logger = logging.getLogger(__name__)

# 创建平台路由器
platform_router = APIRouter(
    prefix="/platform",
    tags=["平台功能"],
    responses={404: {"description": "Not found"}}
)


# ============= 环境管理 =============






# ============= 代码生成 =============

@platform_router.post(
    "/generate_mcp_code",
    summary="🧩 生成MCP代码",
    description="根据curl命令或API描述生成MCP代码",
    operation_id="platform_generate_mcp_code"
)
async def generate_mcp_code_endpoint(
    curl_command: str = Body(..., description="curl命令或API描述"),
    domain: Optional[str] = Body(None, description="业务域，如signing、saas等"),
    method_name: Optional[str] = Body(None, description="方法名称，不提供则自动生成")
):
    """根据curl命令或API描述生成MCP代码"""
    return generate_mcp_code_from_curl(curl_command, domain, method_name)


@platform_router.post(
    "/get_testcase_generation_prompt",
    summary="🎯 获取测试用例生成提示词",
    description="获取资深软件测试专家AI提示词，用于生成完整的测试用例。当检测到关键字'测试用例'时，生成包含9个步骤的完整测试用例生成流程提示词，支持自动生成符合XMind导入标准的markdown文件",
    operation_id="platform_get_testcase_generation_prompt"
)
async def get_testcase_generation_prompt_endpoint():
    """获取测试用例生成提示词 - 资深软件测试专家AI提示词"""
    return get_测试用例生成提示词()


@platform_router.post(
    "/get_httprunner_automation_prompt",
    summary="🚀 获取集测脚本生成提示词",
    description="获取资深软件测试专家AI提示词，用于生成HttpRunner自动化测试用例。包含API信息收集、场景设计、用例生成、覆盖率检查等完整流程，自动生成可执行的YAML格式测试脚本",
    operation_id="platform_get_httprunner_automation_prompt"
)
async def get_httprunner_automation_prompt_endpoint():
    """获取HttpRunner自动化用例生成提示词 - 专业自动化测试AI提示词"""
    return get_HttpRunner自动化用例生成提示词()


@platform_router.post(
    "/get_simple_testcase_prompt",
    summary="📝 获取简版测试用例生成提示词",
    description="获取简化版测试用例生成AI提示词，包含需求分析、场景设计、用例生成等核心流程，快速生成标准格式的测试用例",
    operation_id="platform_get_simple_testcase_prompt"
)
async def get_simple_testcase_prompt_endpoint():
    """获取简版测试用例生成提示词 - 快速测试用例生成AI提示词"""
    return get_简版测试用例生成提示词()


# ============= 提示词管理 =============

# @platform_router.post(
#     "/get_prompt_types",
#     summary="📋 获取提示词类型",
#     description="获取所有可用的提示词类型",
#     operation_id="platform_get_prompt_types"
# )
# async def get_prompt_types_endpoint():
#     """获取所有可用的提示词类型"""
#     return get_prompt_types()
#
#
# @platform_router.post(
#     "/get_prompt_content",
#     summary="📄 获取提示词内容",
#     description="获取指定类型的提示词内容",
#     operation_id="platform_get_prompt_content"
# )
# async def get_prompt_content_endpoint(prompt_type: str):
#     """获取指定类型的提示词内容"""
#     return get_prompt_content(prompt_type)
#
#
#
#
#
# @platform_router.post(
#     "/get_interface_generation_prompt",
#     summary="🔧 获取接口生成提示词",
#     description="获取接口生成提示词",
#     operation_id="platform_get_interface_generation_prompt"
# )
# async def get_interface_generation_prompt_endpoint():
#     """获取接口生成提示词"""
#     return get_接口生成提示词()
#
#
# @platform_router.post(
#     "/get_required_params_prompt",
#     summary="📝 获取必填参数提示词",
#     description="获取必填参数提示词",
#     operation_id="platform_get_required_params_prompt"
# )
# async def get_required_params_prompt_endpoint():
#     """获取必填参数提示词"""
#     return get_必填参数提示词()
#
#
# @platform_router.post(
#     "/get_enum_values_prompt",
#     summary="📊 获取枚举值提示词",
#     description="获取枚举值提示词",
#     operation_id="platform_get_enum_values_prompt"
# )
# async def get_enum_values_prompt_endpoint():
#     """获取枚举值提示词"""
#     return get_枚举值提示词()
#
#
# @platform_router.post(
#     "/get_required_enum_merged_prompt",
#     summary="📋 获取必填与枚举合并提示词",
#     description="获取必填与枚举合并提示词",
#     operation_id="platform_get_required_enum_merged_prompt"
# )
# async def get_required_enum_merged_prompt_endpoint():
#     """获取必填与枚举合并提示词"""
#     return get_必填与枚举合并提示词()
#
#
# @platform_router.post(
#     "/get_general_httprunner_prompt",
#     summary="🧪 获取通用HttpRunner测试用例生成提示词",
#     description="获取通用HttpRunner测试用例生成提示词",
#     operation_id="platform_get_general_httprunner_prompt"
# )
# async def get_general_httprunner_prompt_endpoint():
#     """获取通用HttpRunner测试用例生成提示词"""
#     return get_通用HttpRunner测试用例生成提示词()

# 导出router供main.py使用
router = platform_router