#!/usr/bin/env python3
"""
连接保持器 - 基于FastMCP框架的连接不断功能
不魔改框架，只在框架基础上添加连接保持机制
"""
import json
import time
import uuid
import os
import asyncio
from typing import Dict, Optional, Any
from pathlib import Path
import logging

logger = logging.getLogger(__name__)

class ConnectionKeeper:
    """连接保持器 - 确保MCP连接不断，支持服务重启恢复"""
    
    def __init__(self, storage_file: str = "data/mcp_connections.json"):
        self.storage_file = Path(storage_file)
        self.storage_file.parent.mkdir(parents=True, exist_ok=True)
        
        # 连接状态数据
        self.connections: Dict[str, Dict[str, Any]] = {}
        self.server_start_time = time.time()
        
        # 配置参数
        self.heartbeat_interval = 30  # 30秒心跳
        self.connection_timeout = 604800  # 7天超时
        self.auto_save_interval = 60  # 1分钟自动保存
        
        logger.info(f"连接保持器初始化完成，存储文件: {self.storage_file}")
    
    async def start_background_tasks(self):
        """启动后台任务"""
        # 加载现有连接
        self._load_connections()
        
        # 启动后台任务
        asyncio.create_task(self._auto_save_task())
        asyncio.create_task(self._cleanup_task())
        asyncio.create_task(self._heartbeat_task())
        
        logger.info("连接保持器后台任务已启动")
    
    def _load_connections(self):
        """从文件加载连接状态"""
        try:
            if self.storage_file.exists():
                with open(self.storage_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                restored_count = 0
                current_time = time.time()
                
                for conn_id, conn_data in data.get("connections", {}).items():
                    last_activity = conn_data.get("last_activity", 0)
                    if current_time - last_activity < self.connection_timeout:
                        # 标记为服务重启，需要重连
                        conn_data["status"] = "reconnecting"
                        conn_data["server_restart_count"] = conn_data.get("server_restart_count", 0) + 1
                        conn_data["last_heartbeat"] = current_time
                        
                        self.connections[conn_id] = conn_data
                        restored_count += 1
                        
                        logger.info(f"恢复连接: {conn_id}, 重启次数: {conn_data['server_restart_count']}")
                
                logger.info(f"从文件恢复了 {restored_count} 个连接")
            else:
                logger.info("连接存储文件不存在，创建新的存储")
                
        except Exception as e:
            logger.error(f"加载连接文件失败: {e}")
            self.connections = {}
    
    def _save_connections(self):
        """保存连接状态到文件"""
        try:
            data = {
                "last_updated": time.time(),
                "server_start_time": self.server_start_time,
                "total_connections": len(self.connections),
                "connections": self.connections
            }
            
            # 原子写入
            temp_file = self.storage_file.with_suffix('.tmp')
            with open(temp_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
            
            temp_file.replace(self.storage_file)
            logger.debug(f"连接状态已保存: {len(self.connections)} 个连接")
            
        except Exception as e:
            logger.error(f"保存连接文件失败: {e}")
    
    async def register_connection(self, client_id: str = None, client_info: Dict[str, Any] = None) -> str:
        """注册新连接"""
        conn_id = str(uuid.uuid4())
        client_id = client_id or f"client_{int(time.time())}"
        
        conn_data = {
            "connection_id": conn_id,
            "client_id": client_id,
            "created_at": time.time(),
            "last_heartbeat": time.time(),
            "last_activity": time.time(),
            "status": "active",
            "client_info": client_info or {},
            "server_restart_count": 0,
            "heartbeat_count": 0
        }
        
        self.connections[conn_id] = conn_data
        self._save_connections()
        
        logger.info(f"注册新连接: {conn_id} for client: {client_id}")
        return conn_id
    
    async def update_connection_activity(self, conn_id: str) -> bool:
        """更新连接活动时间"""
        if conn_id in self.connections:
            self.connections[conn_id]["last_activity"] = time.time()
            self.connections[conn_id]["last_heartbeat"] = time.time()
            self.connections[conn_id]["heartbeat_count"] += 1
            
            if self.connections[conn_id]["status"] == "reconnecting":
                self.connections[conn_id]["status"] = "active"
                logger.info(f"连接重连成功: {conn_id}")
            
            return True
        return False
    
    async def get_connection_status(self, conn_id: str = None) -> Dict[str, Any]:
        """获取连接状态"""
        if conn_id and conn_id in self.connections:
            conn = self.connections[conn_id]
            return {
                "connection_id": conn_id,
                "status": conn["status"],
                "client_id": conn["client_id"],
                "created_at": conn["created_at"],
                "last_heartbeat": conn["last_heartbeat"],
                "last_activity": conn["last_activity"],
                "heartbeat_count": conn["heartbeat_count"],
                "server_restart_count": conn["server_restart_count"],
                "uptime": time.time() - conn["created_at"],
                "server_uptime": time.time() - self.server_start_time
            }
        else:
            # 返回服务器整体状态
            active_connections = len([c for c in self.connections.values() if c["status"] == "active"])
            reconnecting_connections = len([c for c in self.connections.values() if c["status"] == "reconnecting"])
            
            return {
                "server_status": "running",
                "server_uptime": time.time() - self.server_start_time,
                "total_connections": len(self.connections),
                "active_connections": active_connections,
                "reconnecting_connections": reconnecting_connections,
                "heartbeat_interval": self.heartbeat_interval,
                "connection_timeout": self.connection_timeout,
                "persistent_storage": True,
                "auto_reconnect": True
            }
    
    async def remove_connection(self, conn_id: str):
        """移除连接"""
        if conn_id in self.connections:
            del self.connections[conn_id]
            self._save_connections()
            logger.info(f"连接已移除: {conn_id}")
    
    async def _auto_save_task(self):
        """自动保存任务"""
        while True:
            try:
                await asyncio.sleep(self.auto_save_interval)
                self._save_connections()
            except Exception as e:
                logger.error(f"自动保存任务错误: {e}")
    
    async def _cleanup_task(self):
        """清理过期连接任务"""
        while True:
            try:
                await asyncio.sleep(300)  # 5分钟清理一次
                
                current_time = time.time()
                expired_connections = []
                
                for conn_id, conn in self.connections.items():
                    if current_time - conn["last_activity"] > self.connection_timeout:
                        expired_connections.append(conn_id)
                
                for conn_id in expired_connections:
                    logger.info(f"清理过期连接: {conn_id}")
                    del self.connections[conn_id]
                
                if expired_connections:
                    self._save_connections()
                    logger.info(f"清理了 {len(expired_connections)} 个过期连接")
                
            except Exception as e:
                logger.error(f"清理任务错误: {e}")
    
    async def _heartbeat_task(self):
        """心跳任务 - 维护所有连接的活跃状态"""
        while True:
            try:
                await asyncio.sleep(self.heartbeat_interval)
                
                current_time = time.time()
                for conn_id, conn in self.connections.items():
                    # 更新心跳时间
                    conn["last_heartbeat"] = current_time
                    
                    # 如果连接长时间无活动，标记为需要重连
                    if current_time - conn["last_activity"] > 300:  # 5分钟无活动
                        if conn["status"] == "active":
                            conn["status"] = "idle"
                            logger.debug(f"连接进入空闲状态: {conn_id}")
                
                logger.debug(f"心跳维护完成，活跃连接: {len(self.connections)}")
                
            except Exception as e:
                logger.error(f"心跳任务错误: {e}")

# 全局连接保持器实例
_connection_keeper = None

def get_connection_keeper():
    """获取连接保持器实例"""
    global _connection_keeper
    if _connection_keeper is None:
        _connection_keeper = ConnectionKeeper()
    return _connection_keeper
