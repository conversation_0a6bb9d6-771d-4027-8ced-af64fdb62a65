#!/usr/bin/env python3
"""
环境服务 - 环境管理相关功能
提供环境信息查询、环境切换等功能
"""
import logging
from typing import Dict, Any, Optional
from app.core.config import *
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


def get_environment_info(environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取环境信息

    Args:
        environment: 环境描述，支持自然语言

    Returns:
        环境信息
    """
    try:
        # 如果指定了环境，先检测和设置
        detected_env = "test"
        if environment:
            detected_env = detect_environment_from_text(environment)
            logger.info(f"检测到环境: {detected_env}")

        # 获取当前环境配置
        current_config = get_api_config(detected_env)

        # 获取所有可用环境
        test_config = get_api_config("test")
        mock_config = get_api_config("mock")
        
        return formatter.success(
            data={
                "current_environment": env_manager.current_environment,
                "current_config": current_config,
                "available_environments": {
                    "test": {
                        "name": "测试环境",
                        "description": "测试环境配置",
                        "base_url": test_config.get("base_url", ""),
                        "app_id": test_config.get("app_id", "")
                    },
                    "mock": {
                        "name": "模拟环境",
                        "description": "模拟环境配置",
                        "base_url": mock_config.get("base_url", ""),
                        "app_id": mock_config.get("app_id", "")
                    }
                },
                "environment_detection": {
                    "input": environment,
                    "detected": detected_env,
                    "keywords": {
                        "test": ["测试", "test", "开发", "dev"],
                        "mock": ["模拟", "mock", "仿真", "虚拟", "假"]
                    }
                }
            },
            message=f"当前环境: {env_manager.current_environment}"
        )
        
    except Exception as e:
        logger.error(f"获取环境信息失败: {str(e)}")
        return formatter.error(
            message=f"获取环境信息失败: {str(e)}",
            details={"environment": environment}
        )


def switch_environment(environment: str) -> Dict[str, Any]:
    """
    切换环境

    Args:
        environment: 目标环境

    Returns:
        切换结果
    """
    try:
        # 记录切换前的环境
        previous_env = current_environment

        # 验证环境类型
        if environment not in ["test", "mock"]:
            return formatter.error(
                message=f"不支持的环境类型: {environment}，仅支持 test 或 mock"
            )

        # 执行环境切换（简化版本）
        new_config = get_api_config(environment)

        return formatter.success(
            data={
                "previous_environment": previous_env,
                "current_environment": environment,
                "config": new_config,
                "switch_time": "2025-01-14",
                "affected_services": [
                    "API请求地址",
                    "应用ID配置",
                    "请求头配置"
                ]
            },
            message=f"环境已从 {previous_env} 切换到 {environment}"
        )
            
    except Exception as e:
        logger.error(f"切换环境失败: {str(e)}")
        return formatter.error(
            message=f"切换环境失败: {str(e)}",
            details={"target_environment": environment}
        )


def get_domain_environment_info(domain: str, environment: Optional[str] = None) -> Dict[str, Any]:
    """
    获取业务域环境信息

    Args:
        domain: 业务域名称
        environment: 环境描述

    Returns:
        业务域环境信息
    """
    try:
        # 获取服务配置
        service_config = get_service_config(domain, environment)

        if not service_config:
            return formatter.error(
                message=f"未找到业务域 {domain} 的配置",
                details={"domain": domain, "environment": environment}
            )

        # 获取相关的API地址和应用ID
        api_url = service_config.get("url", "")
        app_id = service_config.get("app_id", "")
        headers = service_config.get("headers", {})
        
        return formatter.success(
            data={
                "domain": domain,
                "service_config": service_config,
                "environment": env_manager.current_environment,
                "api_info": {
                    "base_url": api_url,
                    "app_id": app_id,
                    "headers": headers
                },
                "usage_example": {
                    "curl": f"curl -X POST '{api_url}/api/path' "
                           f"-H 'X-Tsign-Open-App-Id: {app_id}' "
                           f"-H 'Content-Type: application/json' "
                           f"-d '{{}}'",
                    "mcp_call": f"通过MCP调用 {domain} 域的相关工具"
                }
            },
            message=f"业务域 {domain} 环境信息获取成功"
        )
        
    except Exception as e:
        logger.error(f"获取业务域环境信息失败: {str(e)}")
        return formatter.error(
            message=f"获取业务域环境信息失败: {str(e)}",
            details={"domain": domain, "environment": environment}
        )


def validate_environment_config(environment: str) -> Dict[str, Any]:
    """
    验证环境配置
    
    Args:
        environment: 环境名称
        
    Returns:
        验证结果
    """
    try:
        config = settings.get_api_config(environment)

        if not config:
            return formatter.error(
                message=f"环境 {environment} 配置不存在"
            )

        # 验证必要字段
        required_fields = ["base_url", "app_id", "headers"]
        missing_fields = []

        for field in required_fields:
            if field not in config:
                missing_fields.append(field)

        # 验证API地址可访问性（这里只做格式检查）
        base_url = config.get("base_url", "")
        invalid_urls = []

        if not base_url or not base_url.startswith("http"):
            invalid_urls.append(f"base_url: {base_url}")

        # 生成验证报告
        validation_result = {
            "environment": environment,
            "config_exists": True,
            "missing_fields": missing_fields,
            "invalid_urls": invalid_urls,
            "base_url": base_url,
            "app_id": config.get("app_id", ""),
            "is_valid": len(missing_fields) == 0 and len(invalid_urls) == 0
        }
        
        if validation_result["is_valid"]:
            return formatter.success(
                data=validation_result,
                message=f"环境 {environment} 配置验证通过"
            )
        else:
            return formatter.error(
                message=f"环境 {environment} 配置验证失败",
                details=validation_result
            )
            
    except Exception as e:
        logger.error(f"验证环境配置失败: {str(e)}")
        return formatter.error(
            message=f"验证环境配置失败: {str(e)}",
            details={"environment": environment}
        )
