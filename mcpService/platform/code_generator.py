#!/usr/bin/env python3
"""
代码生成器 - 自动生成MCP代码
支持从curl命令、API描述等自动生成MCP代码
"""
import re
import logging
from typing import Dict, Any, Optional
# from mcpService.common.mcp_template import mcp_template  # 已删除
from mcpService.common.response_formatter import formatter

logger = logging.getLogger(__name__)


def detect_mcp_trigger(text: str) -> bool:
    """
    检测是否包含MCP接入触发关键词

    Args:
        text: 输入文本

    Returns:
        是否包含触发关键词
    """
    trigger_keywords = [
        "接入mcp", "接入MCP", "mcp接入", "MCP接入",
        "生成mcp", "生成MCP", "mcp生成", "MCP生成",
        "创建mcp", "创建MCP", "mcp创建", "MCP创建"
    ]

    text_lower = text.lower()
    for keyword in trigger_keywords:
        if keyword.lower() in text_lower:
            return True

    return False


def detect_testcase_trigger(text: str) -> bool:
    """
    检测是否包含测试用例生成触发关键词

    Args:
        text: 输入文本

    Returns:
        是否包含触发关键词
    """
    trigger_keywords = [
        "测试用例", "测试案例", "test case", "testcase",
        "生成测试用例", "创建测试用例", "设计测试用例"
    ]

    text_lower = text.lower()
    for keyword in trigger_keywords:
        if keyword.lower() in text_lower:
            return True

    return False


def extract_curl_from_text(text: str) -> Optional[str]:
    """
    从文本中提取curl命令
    
    Args:
        text: 输入文本
        
    Returns:
        提取的curl命令，如果没有则返回None
    """
    # 匹配curl命令的正则表达式
    curl_patterns = [
        r"curl\s+.*?(?=\n\n|\n$|$)",  # 基本curl命令
        r"curl\s+.*?(?=\n[^\\]|\n$|$)",  # 考虑换行符的curl命令
    ]
    
    for pattern in curl_patterns:
        match = re.search(pattern, text, re.DOTALL | re.MULTILINE)
        if match:
            curl_command = match.group(0).strip()
            # 清理换行符和多余空格
            curl_command = re.sub(r'\\\s*\n\s*', ' ', curl_command)
            curl_command = re.sub(r'\s+', ' ', curl_command)
            return curl_command
    
    return None


def generate_mcp_code_from_curl(
    input_text: str,
    domain: Optional[str] = None,
    method_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    从输入生成MCP代码提示词

    Args:
        input_text: 输入文本，可以是curl命令、python方法、swagger等
        domain: 业务域
        method_name: 方法名称

    Returns:
        生成结果
    """
    try:
        # 检测是否包含MCP触发关键词
        if not detect_mcp_trigger(input_text):
            return formatter.error(
                message="未检测到MCP接入触发关键词",
                details={
                    "trigger_keywords": ["接入mcp", "生成mcp", "创建mcp"],
                    "input_text": input_text[:200] + "..." if len(input_text) > 200 else input_text,
                    "help": "请在输入中包含'接入mcp'关键词来触发代码生成"
                }
            )

        # 生成MCP代码生成提示词
        prompt_template = f"""
# MCP代码生成提示词

## 任务描述
根据以下输入内容，生成对应的MCP代码，包括服务层代码和控制器代码。

## 输入内容
```
{input_text}
```

## 生成要求

### 1. 分析输入内容
- 如果是curl命令，解析URL、方法、参数、请求头
- 如果是Python方法，分析方法名、参数、返回值
- 如果是Swagger文档，提取接口信息
- 如果是其他格式，根据内容推断接口信息

### 2. 生成服务层代码
```python
def {{method_name}}(
    # 根据输入内容推断的参数
    param1: str,
    param2: Optional[str] = None,
    environment: Optional[str] = None
) -> Dict[str, Any]:
    \"\"\"
    {{方法描述}}

    Args:
        param1: 参数1描述
        param2: 参数2描述
        environment: 环境描述，支持自然语言

    Returns:
        API调用结果
    \"\"\"
    try:
        # 构建请求数据
        request_data = {{
            "param1": param1,
            "param2": param2
        }}

        # 发送API请求
        result = service.make_api_request(
            path="{{api_path}}",
            data=request_data,
            method="{{http_method}}",
            environment=environment,
            operation="{{operation_name}}"
        )

        return result

    except Exception as e:
        logger.error(f"{{method_name}}异常: {{str(e)}}")
        return service.formatter.error(
            message=f"{{method_name}}异常: {{str(e)}}",
            details={{"method": "{{method_name}}", "domain": "{{domain}}"}}
        )
```

### 3. 生成控制器代码
```python
@{{domain}}_router.post(
    "/{{endpoint_path}}",
    summary="🔧 {{method_description}}",
    description="{{method_description}}",
    operation_id="{{domain}}_{{method_name}}"
)
async def {{method_name}}_endpoint(
    param1: str,
    param2: Optional[str] = None,
    environment: Optional[str] = Query(None, description="环境类型")
):
    \"\"\"{{method_description}}\"\"\"
    return {{method_name}}(param1, param2, environment)
```

### 4. 生成集成指南
提供详细的代码集成步骤，包括：
- 文件位置建议
- 导入语句
- 路由注册
- 测试方法

## 业务域建议
{f"建议业务域: {domain}" if domain else "请根据API内容推断合适的业务域"}

## 输出格式
请按照以上模板生成完整的MCP代码，并提供详细的集成指南。
"""

        return formatter.success(
            data={
                "prompt": prompt_template,
                "input_analysis": {
                    "input_type": _detect_input_type(input_text),
                    "suggested_domain": domain or _suggest_domain(input_text),
                    "suggested_method": method_name or _suggest_method_name(input_text)
                },
                "generation_guide": {
                    "step1": "使用上述提示词让AI生成MCP代码",
                    "step2": "将生成的服务层代码添加到对应的service文件",
                    "step3": "将生成的控制器代码添加到对应的controller文件",
                    "step4": "按照集成指南完成代码集成",
                    "step5": "测试生成的接口功能"
                }
            },
            message="MCP代码生成提示词已生成，请使用AI工具处理"
        )

    except Exception as e:
        logger.error(f"生成MCP代码提示词失败: {str(e)}")
        return formatter.error(
            message=f"生成MCP代码提示词失败: {str(e)}",
            details={
                "input_text": input_text[:200] + "..." if len(input_text) > 200 else input_text
            }
        )


def _detect_input_type(text: str) -> str:
    """检测输入类型"""
    text_lower = text.lower()
    if "curl" in text_lower:
        return "curl_command"
    elif "def " in text and "(" in text:
        return "python_method"
    elif "swagger" in text_lower or "openapi" in text_lower:
        return "swagger_doc"
    elif "http" in text_lower and ("get" in text_lower or "post" in text_lower):
        return "http_request"
    else:
        return "unknown"


def _suggest_domain(text: str) -> str:
    """建议业务域"""
    text_lower = text.lower()
    if any(keyword in text_lower for keyword in ["cert", "certificate", "证书"]):
        return "certificate"
    elif any(keyword in text_lower for keyword in ["sign", "签署", "签名"]):
        return "signing"
    elif any(keyword in text_lower for keyword in ["account", "user", "账号", "用户"]):
        return "saas"
    elif any(keyword in text_lower for keyword in ["identity", "real", "实名", "认证"]):
        return "identity"
    elif any(keyword in text_lower for keyword in ["intention", "意愿", "确认"]):
        return "intention"
    else:
        return "common"


def _suggest_method_name(text: str) -> str:
    """建议方法名"""
    # 从URL路径中提取方法名
    import re
    url_match = re.search(r"https?://[^/]+/(.+?)(?:\s|$|'|\")", text)
    if url_match:
        path = url_match.group(1)
        # 清理路径，生成方法名
        method_name = re.sub(r"[^a-zA-Z0-9_]", "_", path)
        method_name = re.sub(r"_+", "_", method_name).strip("_")
        return method_name or "api_call"

    return "api_call"


def generate_mcp_code_from_api_spec(
    api_spec: Dict[str, Any],
    domain: Optional[str] = None,
    method_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    从API规范生成MCP代码
    
    Args:
        api_spec: API规范字典
        domain: 业务域
        method_name: 方法名称
        
    Returns:
        生成结果
    """
    try:
        # 构建curl命令
        url = api_spec.get("url", "")
        method = api_spec.get("method", "POST")
        headers = api_spec.get("headers", {})
        data = api_spec.get("data", {})
        
        # 构建curl命令字符串
        curl_parts = ["curl"]
        
        # 添加URL
        curl_parts.append(f"'{url}'")
        
        # 添加方法
        if method.upper() != "GET":
            curl_parts.append(f"--request {method.upper()}")
        
        # 添加请求头
        for key, value in headers.items():
            curl_parts.append(f"--header '{key}: {value}'")
        
        # 添加数据
        if data and method.upper() != "GET":
            import json
            data_str = json.dumps(data, ensure_ascii=False)
            curl_parts.append(f"--data-raw '{data_str}'")
        
        curl_command = " \\\n".join(curl_parts)
        
        # 使用curl命令生成代码 (暂时禁用，mcp_template已删除)
        return {
            "status": "success",
            "message": "代码生成功能暂时禁用",
            "data": {
                "curl_command": curl_command,
                "domain": domain,
                "method_name": method_name
            }
        }
        
    except Exception as e:
        logger.error(f"从API规范生成MCP代码失败: {str(e)}")
        return formatter.error(
            message=f"从API规范生成MCP代码失败: {str(e)}",
            details={"api_spec": api_spec}
        )


def generate_mcp_code_interactive(
    description: str,
    url: str,
    method: str = "POST",
    parameters: Optional[Dict[str, Any]] = None,
    domain: Optional[str] = None
) -> Dict[str, Any]:
    """
    交互式生成MCP代码
    
    Args:
        description: 接口描述
        url: 接口地址
        method: 请求方法
        parameters: 参数字典
        domain: 业务域
        
    Returns:
        生成结果
    """
    try:
        # 构建API规范
        api_spec = {
            "url": url,
            "method": method,
            "headers": {
                "Content-Type": "application/json"
            },
            "data": parameters or {}
        }
        
        # 从描述中提取方法名
        method_name = re.sub(r"[^a-zA-Z0-9_\u4e00-\u9fa5]", "_", description)
        method_name = re.sub(r"_+", "_", method_name).strip("_")
        
        # 生成代码
        result = generate_mcp_code_from_api_spec(
            api_spec=api_spec,
            domain=domain,
            method_name=method_name
        )
        
        if result.get("status") == "success":
            result["data"]["description"] = description
            result["data"]["interactive_generated"] = True
        
        return result
        
    except Exception as e:
        logger.error(f"交互式生成MCP代码失败: {str(e)}")
        return formatter.error(
            message=f"交互式生成MCP代码失败: {str(e)}",
            details={
                "description": description,
                "url": url,
                "method": method,
                "parameters": parameters
            }
        )
