"""
平台服务模块
提供平台级功能：环境管理、代码生成、提示词管理等
"""

from .environment_service import *
from .code_generator import *
from .prompt_service import *

__all__ = [
    # 环境管理
    "get_environment_info", "switch_environment",

    # 代码生成
    "generate_mcp_code_from_curl",

    # 提示词管理
    "get_prompt_types", "get_prompt_content", "search_prompts",
    "get_接口生成提示词", "get_必填参数提示词", "get_枚举值提示词",
    "get_必填与枚举合并提示词", "get_通用HttpRunner测试用例生成提示词",
    "get_全套集测提示词", "get_测试用例生成提示词", "get_HttpRunner自动化用例生成提示词"
]
