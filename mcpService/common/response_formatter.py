#!/usr/bin/env python3
"""
响应格式化器 - 统一响应格式处理
提供标准化的响应格式和错误处理
"""
import logging
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)


class ResponseFormatter:
    """响应格式化器"""
    
    @staticmethod
    def success(
        data: Any = None, 
        message: str = "操作成功",
        extra_info: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化成功响应
        
        Args:
            data: 响应数据
            message: 响应消息
            extra_info: 额外信息
            
        Returns:
            标准化成功响应
        """
        response = {
            "status": "success",
            "message": message,
            "timestamp": datetime.now().isoformat(),
            "data": data
        }
        
        if extra_info:
            response.update(extra_info)
            
        return response
    
    @staticmethod
    def error(
        message: str = "操作失败",
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        格式化错误响应
        
        Args:
            message: 错误消息
            error_code: 错误代码
            details: 错误详情
            
        Returns:
            标准化错误响应
        """
        response = {
            "status": "error",
            "message": message,
            "timestamp": datetime.now().isoformat()
        }
        
        if error_code:
            response["error_code"] = error_code
            
        if details:
            response["details"] = details
            
        return response
    
    @staticmethod
    def format_api_response(
        api_result: Dict[str, Any],
        success_message: str = "API调用成功",
        error_message: str = "API调用失败"
    ) -> Dict[str, Any]:
        """
        格式化API响应
        
        Args:
            api_result: API原始响应
            success_message: 成功消息
            error_message: 失败消息
            
        Returns:
            格式化后的响应
        """
        try:
            # 检查是否是错误响应
            if api_result.get("status") == "error":
                error_response = ResponseFormatter.error(
                    message=api_result.get("message", error_message),
                    details=api_result
                )
                # 保留请求详情
                if "request_details" in api_result:
                    error_response["request_details"] = api_result["request_details"]
                if "response_details" in api_result:
                    error_response["response_details"] = api_result["response_details"]
                return error_response

            # 检查HTTP状态码
            response_info = api_result.get("response_details", {})
            status_code = response_info.get("status_code", 200)

            if status_code >= 400:
                error_response = ResponseFormatter.error(
                    message=f"HTTP错误: {status_code}",
                    details=api_result
                )
                # 保留请求详情
                if "request_details" in api_result:
                    error_response["request_details"] = api_result["request_details"]
                if "response_details" in api_result:
                    error_response["response_details"] = api_result["response_details"]
                return error_response

            # 检查业务状态
            if "success" in api_result:
                if api_result["success"]:
                    success_response = ResponseFormatter.success(
                        data=api_result,
                        message=success_message
                    )
                    # 添加请求详情
                    if "request_details" in api_result:
                        success_response["request_details"] = api_result["request_details"]
                    if "response_details" in api_result:
                        success_response["response_details"] = api_result["response_details"]
                    return success_response
                else:
                    error_response = ResponseFormatter.error(
                        message=api_result.get("message", error_message),
                        details=api_result
                    )
                    # 保留请求详情
                    if "request_details" in api_result:
                        error_response["request_details"] = api_result["request_details"]
                    if "response_details" in api_result:
                        error_response["response_details"] = api_result["response_details"]
                    return error_response

            # 检查其他成功标识
            if "code" in api_result:
                if api_result["code"] == 0 or api_result["code"] == "0":
                    success_response = ResponseFormatter.success(
                        data=api_result,
                        message=success_message
                    )
                    # 添加请求详情
                    if "request_details" in api_result:
                        success_response["request_details"] = api_result["request_details"]
                    if "response_details" in api_result:
                        success_response["response_details"] = api_result["response_details"]
                    return success_response
                else:
                    error_response = ResponseFormatter.error(
                        message=api_result.get("message", error_message),
                        error_code=str(api_result["code"]),
                        details=api_result
                    )
                    # 保留请求详情
                    if "request_details" in api_result:
                        error_response["request_details"] = api_result["request_details"]
                    if "response_details" in api_result:
                        error_response["response_details"] = api_result["response_details"]
                    return error_response

            # 默认认为成功
            success_response = ResponseFormatter.success(
                data=api_result,
                message=success_message
            )
            # 添加请求详情
            if "request_details" in api_result:
                success_response["request_details"] = api_result["request_details"]
            if "response_details" in api_result:
                success_response["response_details"] = api_result["response_details"]
            return success_response
            
        except Exception as e:
            logger.error(f"响应格式化异常: {str(e)}")
            return ResponseFormatter.error(
                message=f"响应格式化异常: {str(e)}",
                details={"original_response": api_result}
            )
    
    @staticmethod
    def format_mcp_response(
        data: Any,
        operation: str,
        environment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化MCP响应
        
        Args:
            data: 响应数据
            operation: 操作名称
            environment: 环境信息
            
        Returns:
            MCP标准响应
        """
        response = ResponseFormatter.success(
            data=data,
            message=f"{operation}执行成功"
        )
        
        # 添加MCP特有信息
        response["mcp_info"] = {
            "operation": operation,
            "environment": environment or "default",
            "platform": "esign-qa-mcp-platform",
            "version": "2.0.0"
        }
        
        return response


# 全局响应格式化器实例
formatter = ResponseFormatter()
