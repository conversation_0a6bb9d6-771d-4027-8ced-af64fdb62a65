#!/usr/bin/env python3
"""
基础服务类 - 服务层基类
提供通用的服务功能和工具方法
"""
import logging
from typing import Dict, Any, Optional
from .http_client import http_client
from .response_formatter import formatter
from app.core.config import *

logger = logging.getLogger(__name__)


class BaseService:
    """基础服务类"""
    
    def __init__(self, domain: str):
        """
        初始化基础服务
        
        Args:
            domain: 业务域名称
        """
        self.domain = domain
        self.http_client = http_client
        # 移除env_manager依赖
        self.formatter = formatter
    
    def get_api_config(self, environment: Optional[str] = None) -> Dict[str, Any]:
        """
        获取API配置

        Args:
            environment: 环境描述

        Returns:
            API配置
        """
        return get_api_config(environment)

    def get_api_url(self, service: str = "footstone", environment: Optional[str] = None) -> str:
        """
        获取API地址

        Args:
            service: 服务类型
            environment: 环境描述

        Returns:
            API地址
        """
        api_config = self.get_api_config(environment)
        
        
        return api_config.get("base_url", "")
       

    def get_app_id(self, app_type: str = "default", environment: Optional[str] = None) -> str:
        """
        获取应用ID

        Args:
            app_type: 应用类型
            environment: 环境描述

        Returns:
            应用ID
        """
        api_config = self.get_api_config(environment)

        if app_type == "cert":
            return api_config.get("cert_app_id", "")
        else:
            return api_config.get("app_id", "")
    
    def make_api_request(
        self,
        path: str = None,
        url: str = None,
        data: Optional[Dict[str, Any]] = None,
        method: str = "POST",
        headers: Optional[Dict[str, str]] = None,
        params: Optional[Dict[str, Any]] = None,
        environment: Optional[str] = None,
        service: str = "sdk",
        operation: str = "API调用"
    ) -> Dict[str, Any]:
        """
        发送API请求

        Args:
            path: API路径 (与url二选一)
            url: 完整URL (与path二选一，优先使用)
            data: 请求数据
            method: 请求方法
            headers: 请求头
            params: URL参数
            environment: 环境描述
            service: 服务类型
            operation: 操作名称

        Returns:
            API响应
        """
        try:
            # 确定最终URL
            if url:
                # 使用完整URL
                final_url = url
            elif path:
                # 使用路径构建URL
                base_url = self.get_api_url(service, environment)
                if not base_url:
                    error_response = self.formatter.error(f"无法获取{service}服务的API地址")
                    # 添加请求详情，即使没有实际发送请求
                    error_response["request_details"] = {
                        "url": f"未知/{path.lstrip('/')}",
                        "method": method,
                        "headers": headers or {},
                        "data": data,
                        "params": params,
                        "environment": environment or "default",
                        "domain": self.domain,
                        "service": service
                    }
                    return error_response

                # 构建完整URL
                final_url = f"{base_url.rstrip('/')}/{path.lstrip('/')}"
            else:
                error_response = self.formatter.error("必须提供path或url参数")
                error_response["request_details"] = {
                    "url": "未提供",
                    "method": method,
                    "headers": headers or {},
                    "data": data,
                    "params": params,
                    "environment": environment or "default",
                    "domain": self.domain,
                    "service": service
                }
                return error_response

            # 发送请求
            result = self.http_client.make_request(
                url=final_url,
                method=method,
                data=data,
                headers=headers,
                params=params,
                environment=environment,
                domain=self.domain
            )
            
            # 格式化响应
            return self.formatter.format_api_response(
                api_result=result,
                success_message=f"{operation}成功",
                error_message=f"{operation}失败"
            )
            
        except Exception as e:
            logger.error(f"{operation}异常: {str(e)}")
            error_response = self.formatter.error(
                message=f"{operation}异常: {str(e)}",
                details={"domain": self.domain, "path": path}
            )
            # 添加请求详情
            error_response["request_details"] = {
                "url": f"异常/{path.lstrip('/')}",
                "method": method,
                "headers": headers or {},
                "data": data,
                "params": params,
                "environment": environment or "default",
                "domain": self.domain,
                "service": service
            }
            return error_response
    
    def format_response(
        self,
        data: Any,
        operation: str,
        environment: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        格式化响应
        
        Args:
            data: 响应数据
            operation: 操作名称
            environment: 环境描述
            
        Returns:
            格式化后的响应
        """
        return self.formatter.format_mcp_response(data, operation, environment)
