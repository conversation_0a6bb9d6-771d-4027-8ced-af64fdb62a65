#!/usr/bin/env python3
"""
通用工具模块 - 统一HTTP请求处理和配置管理
"""
import requests
import logging
from typing import Dict, Any, Optional
from app.core.config import *

# 配置日志
logger = logging.getLogger(__name__)


def get_headers(app_id: Optional[str] = None, service_group: Optional[str] = None, environment: Optional[str] = None) -> Dict[str, str]:
    """
    获取请求头

    :param app_id: 应用ID，如果不提供则从配置中获取
    :param service_group: 服务组，如果不提供则使用默认值
    :param environment: 环境类型
    :return: 请求头字典
    """
    # 获取API配置
    api_config = get_api_config(environment)

    # 使用提供的app_id或从配置中获取
    final_app_id = app_id or api_config.get("app_id", APP_ID)
    final_service_group = service_group or "DEFAULT"

    headers = {
        "Content-Type": "application/json",
        "X-Tsign-Open-App-Id": final_app_id,
        "X-Tsign-Open-Auth-Mode": "simple",
        "filter-result": "false",
        "X-Tsign-Service-Group": final_service_group
    }

    # 添加租户ID
    if X_TSIGN_OPEN_TENANT_ID:
        headers["X-Tsign-Open-Tenant-Id"] = X_TSIGN_OPEN_TENANT_ID

    return headers


def get_service_url(service: str, environment: Optional[str] = None) -> str:
    """
    获取服务URL

    :param service: 服务名称
    :param environment: 环境类型
    :return: 服务URL
    """
    service_config = get_service_config(service, environment)
    return service_config.get("url", "")


def make_http_request(url: str, data: Optional[Dict[str, Any]] = None,
                      headers: Optional[Dict[str, str]] = None,
                      method: str = "POST", timeout: Optional[int] = None,
                      environment: Optional[str] = None) -> Dict[str, Any]:
    """
    通用HTTP请求方法

    :param url: 请求URL
    :param data: 请求数据
    :param headers: 请求头
    :param method: 请求方法
    :param timeout: 超时时间
    :param environment: 环境类型
    :return: 响应结果
    """
    try:
        # 使用配置中的超时时间
        final_timeout = timeout or REQUEST_TIMEOUT

        # 获取默认请求头
        default_headers = get_headers(environment=environment)
        request_headers = default_headers.copy()
        if headers:
            request_headers.update(headers)

        logger.info(f"发起HTTP请求: {method} {url}")
        logger.debug(f"请求头: {request_headers}")
        logger.debug(f"请求数据: {data}")

        if method.upper() == "POST":
            response = requests.post(url, json=data, headers=request_headers, timeout=final_timeout)
        elif method.upper() == "GET":
            response = requests.get(url, params=data, headers=request_headers, timeout=final_timeout)
        elif method.upper() == "PUT":
            response = requests.put(url, json=data, headers=request_headers, timeout=final_timeout)
        elif method.upper() == "DELETE":
            response = requests.delete(url, json=data, headers=request_headers, timeout=final_timeout)
        else:
            raise ValueError(f"不支持的请求方法: {method}")

        # 检查响应状态
        response.raise_for_status()

        # 记录请求日志
        log_request(url, method, request_headers, data, response.status_code, response.text)

        # 尝试解析JSON响应
        try:
            result = response.json()
        except ValueError:
            result = {"text": response.text, "status_code": response.status_code}

        # 添加请求详情和响应详情到响应中
        if isinstance(result, dict):
            result["request_details"] = {
                "url": url,
                "method": method,
                "headers": request_headers,
                "data": data,
                "environment": environment or "default"
            }
            result["response_details"] = {
                "status_code": response.status_code,
                "headers": dict(response.headers),
                "response_time": "N/A"  # 可以后续添加响应时间计算
            }

        return result

    except requests.exceptions.RequestException as e:
        logger.error(f"HTTP请求失败: {str(e)}")

        # 确保使用合并后的请求头
        final_headers = request_headers if 'request_headers' in locals() else (headers or {})

        # 尝试获取响应信息
        response_details = {}
        if hasattr(e, 'response') and e.response is not None:
            response_details = {
                "status_code": e.response.status_code,
                "headers": dict(e.response.headers),
                "response_time": "N/A"
            }

        error_response = {
            "status": "error",
            "message": f"请求失败: {str(e)}",
            "request_details": {
                "url": url,
                "method": method,
                "headers": final_headers,
                "data": data,
                "environment": environment or "default"
            }
        }

        # 添加响应详情（如果有的话）
        if response_details:
            error_response["response_details"] = response_details

        # 记录错误日志
        status_code = response_details.get("status_code") if response_details else None
        log_request(url, method, final_headers, data, status_code, str(e))

        return error_response


def log_request(url: str, method: str, headers: Dict[str, str],
                input_data: Optional[Dict[str, Any]], status_code: Optional[int],
                output: str) -> None:
    """
    记录请求日志

    :param url: 请求URL
    :param method: 请求方法
    :param headers: 请求头
    :param input_data: 请求数据
    :param status_code: 响应状态码
    :param output: 响应内容
    """
    try:
        # 使用标准日志记录请求信息
        log_message = f"HTTP请求 - {method} {url} - 状态码: {status_code}"

        if settings.LOG_REQUESTS:
            logger.info(log_message)
            logger.debug(f"请求头: {headers}")
            logger.debug(f"请求数据: {input_data}")
            logger.debug(f"响应内容: {output}")

    except Exception as e:
        logger.error(f"记录请求日志失败: {str(e)}")


def get_test_environment_url(service_type: str = "api", environment: Optional[str] = None) -> str:
    """
    获取测试环境URL

    :param service_type: 服务类型 (api, cert, footstone_user, sdk等)
    :param environment: 环境类型
    :return: 环境URL
    """
    return get_service_url(service_type, environment)


def format_response(status: str, data: Any = None, message: str = "", 
                   extra_info: Optional[Dict[str, Any]] = None) -> Dict[str, Any]:
    """
    格式化响应结果
    
    :param status: 状态 (success/error)
    :param data: 响应数据
    :param message: 响应消息
    :param extra_info: 额外信息
    :return: 格式化的响应
    """
    response = {
        "status": status,
        "message": message
    }
    
    if data is not None:
        response["data"] = data
        
    if extra_info:
        response.update(extra_info)
        
    return response


def validate_required_params(params: Dict[str, Any], required_fields: list) -> Optional[str]:
    """
    验证必需参数
    
    :param params: 参数字典
    :param required_fields: 必需字段列表
    :return: 错误消息，如果验证通过则返回None
    """
    missing_fields = []
    for field in required_fields:
        if field not in params or params[field] is None or params[field] == "":
            missing_fields.append(field)
    
    if missing_fields:
        return f"缺少必需参数: {', '.join(missing_fields)}"
    
    return None
