#!/usr/bin/env python3
"""
测试通过文件创建签署流程
"""
import requests
import json

def test_create_flow_by_file():
    """测试创建签署流程"""
    
    # API地址
    url = "http://localhost:8000/signing/create_flow_by_file"
    
    # 请求数据 - 使用默认值
    data = {
        "file_id": "63573ed91cee4166b532c87a6e83a7c1",
        "file_name": "测试合同.pdf",
        "signer_account_id": "6300761d28b6447b98eea4589db35a18",
        "signer_name": "测试用户",
        "signer_mobile": "***********",
        "signer_idcard": "340822199712270158",
        "flow_title": "MCP工具测试签署流程",
        "pos_x": 200,
        "pos_y": 600,
        "pos_page": "1",
        "environment": "测试环境"
    }
    
    try:
        print("正在创建签署流程...")
        print(f"请求URL: {url}")
        print(f"请求数据: {json.dumps(data, ensure_ascii=False, indent=2)}")
        
        response = requests.post(url, json=data)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print("\n✅ 签署流程创建成功!")
            
            # 提取flowId
            if "data" in result and "flowId" in result["data"]:
                flow_id = result["data"]["flowId"]
                print(f"🎯 Flow ID: {flow_id}")
                return flow_id
            else:
                print("⚠️ 响应中未找到flowId")
                return None
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return None
            
    except Exception as e:
        print(f"❌ 请求异常: {str(e)}")
        return None

if __name__ == "__main__":
    flow_id = test_create_flow_by_file()
    if flow_id:
        print(f"\n🎉 签署流程创建成功，Flow ID: {flow_id}")
    else:
        print("\n💥 签署流程创建失败")
